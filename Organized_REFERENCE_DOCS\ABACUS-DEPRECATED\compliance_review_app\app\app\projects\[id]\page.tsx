import Link from "next/link"

export default function ProjectDetailPage({ params }: { params: { id: string } }) {
  const project = {
    id: params.id,
    title: "Hurricane Relief Project",
    description: "FEMA Public Assistance project for hurricane damage recovery in coastal areas.",
    status: "In Progress",
    progress: 35,
    createdAt: "June 15, 2023"
  }

  const steps = [
    { id: "1", title: "Project Information", completed: true },
    { id: "2", title: "Eligibility Documentation", completed: false },
    { id: "3", title: "Cost Documentation", completed: false },
    { id: "4", title: "Environmental Review", completed: false },
    { id: "5", title: "Final Review", completed: false }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">F</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                FEMA Compliance
              </span>
            </Link>
            <nav className="flex space-x-1">
              <Link 
                href="/dashboard" 
                className="px-4 py-2 text-gray-700 hover:text-blue-600 text-sm font-medium"
              >
                Dashboard
              </Link>
              <Link 
                href="/projects" 
                className="px-4 py-2 text-gray-700 hover:text-blue-600 text-sm font-medium"
              >
                Projects
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <div className="flex items-center space-x-4">
              <h1 className="text-3xl font-bold text-gray-900">
                {project.title}
              </h1>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                project.status === "Completed" 
                  ? "bg-green-100 text-green-800"
                  : project.status === "Under Review"
                  ? "bg-yellow-100 text-yellow-800" 
                  : "bg-blue-100 text-blue-800"
              }`}>
                {project.status}
              </span>
            </div>
            <p className="text-gray-600 mt-1">
              Created on {project.createdAt}
            </p>
          </div>
          <Link 
            href="/projects"
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm font-medium"
          >
            Back to Projects
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Description
              </h2>
              <p className="text-gray-600">
                {project.description}
              </p>
            </div>

            {/* Progress */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  Compliance Progress
                </h2>
                <span className="text-sm font-medium">
                  {steps.filter(step => step.completed).length} of {steps.length} steps completed
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 mb-6">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
              
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className="flex-shrink-0 mr-4">
                      {step.completed ? (
                        <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600">
                            {index + 1}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex justify-between items-center">
                        <p className={`text-sm font-medium ${
                          step.completed 
                            ? "text-green-600" 
                            : "text-gray-900"
                        }`}>
                          {step.title}
                        </p>
                        <Link 
                          href={`/projects/${params.id}/wizard/${step.id}`}
                          className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 font-medium"
                        >
                          {step.completed ? "Review" : "Start"}
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Documents */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  Documents
                </h2>
                <span className="text-sm font-medium">
                  3 total
                </span>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-gray-50 rounded-md border border-gray-100">
                  <div className="p-2 bg-blue-100 rounded-md mr-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      Damage Assessment Report.pdf
                    </p>
                    <p className="text-xs text-gray-500">
                      Uploaded on June 16, 2023
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 rounded-md border border-gray-100">
                  <div className="p-2 bg-blue-100 rounded-md mr-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      Cost Estimate Worksheet.xlsx
                    </p>
                    <p className="text-xs text-gray-500">
                      Uploaded on June 18, 2023
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 rounded-md border border-gray-100">
                  <div className="p-2 bg-blue-100 rounded-md mr-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      Project Photos.zip
                    </p>
                    <p className="text-xs text-gray-500">
                      Uploaded on June 20, 2023
                    </p>
                  </div>
                </div>
              </div>
              
              <Link 
                href={`/projects/${params.id}/documents`}
                className="block w-full mt-4 px-4 py-2 bg-blue-600 text-white text-center rounded-md hover:bg-blue-700 text-sm font-medium"
              >
                Manage Documents
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}