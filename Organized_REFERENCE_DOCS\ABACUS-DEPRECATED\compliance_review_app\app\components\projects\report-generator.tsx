"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  CheckCircle2, 
  Download, 
  FileText, 
  Printer 
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { getProject } from "@/lib/dummy-data"

interface ReportGeneratorProps {
  projectId: string
}

export function ReportGenerator({ projectId }: ReportGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [reportGenerated, setReportGenerated] = useState(false)
  const { toast } = useToast()
  const project = getProject(projectId)

  const generateReport = () => {
    setIsGenerating(true)
    
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false)
      setReportGenerated(true)
      toast({
        title: "Report Generated",
        description: "Your compliance report has been successfully generated.",
      })
    }, 2500)
  }

  const downloadReport = () => {
    toast({
      title: "Download Started",
      description: "Your report is being downloaded.",
    })
  }

  const printReport = () => {
    toast({
      title: "Print Prepared",
      description: "Your report is ready to print.",
    })
  }

  if (!project) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-heading">
        Compliance Report Generator
      </h2>
      
      <div className="space-y-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
          <div className="flex items-start">
            <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-4">
              <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white font-heading">
                {project.title} - Compliance Report
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Generate a comprehensive compliance report for your FEMA Public Assistance project.
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center">
          {!reportGenerated ? (
            <Button
              onClick={generateReport}
              disabled={isGenerating}
              className="gradient-button"
            >
              {isGenerating ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating Report...
                </div>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Compliance Report
                </>
              )}
            </Button>
          ) : (
            <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg border border-green-200 dark:border-green-800 flex items-center">
              <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400 mr-3" />
              <span className="text-sm text-green-800 dark:text-green-300 font-medium">
                Report generated successfully!
              </span>
            </div>
          )}
        </div>

        {reportGenerated && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.5 }}
            className="p-5 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-heading">
              Report Actions
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                variant="outline"
                onClick={downloadReport}
                className="flex items-center justify-center shadow-soft hover:shadow-medium transition-all"
              >
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              <Button
                variant="outline"
                onClick={printReport}
                className="flex items-center justify-center shadow-soft hover:shadow-medium transition-all"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Report
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}