const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  distDir: process.env.NEXT_DIST_DIR || '.next',
  output: process.env.NEXT_OUTPUT_MODE,
  outputFileTracingRoot: path.join(__dirname, '../'),
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config) => {
    config.resolve.fallback = { ...config.resolve.fallback, 
      "react-remove-scroll-bar": false,
      "react-style-singleton": false,
      "use-callback-ref": false,
      "use-sidecar": false,
      "enhanced-resolve": false
    };
    
    // Add proper path alias resolution
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.join(__dirname, './')
    };
    
    return config;
  },
  images: { unoptimized: true },
};

module.exports = nextConfig;
