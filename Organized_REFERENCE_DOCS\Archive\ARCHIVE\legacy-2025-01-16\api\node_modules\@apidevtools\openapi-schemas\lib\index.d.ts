import { JsonSchemaDraft4, JsonSchemaDraft202012 } from "./json-schema";
export { JsonSchemaDraft4, JsonSchemaDraft202012 };
/**
 * JSON Schema for OpenAPI Specification v1.2
 */
export declare const openapiV1: JsonSchemaDraft4;
/**
 * JSON Schema for OpenAPI Specification v2.0
 */
export declare const openapiV2: JsonSchemaDraft4;
/**
 * JSON Schema for OpenAPI Specification v3.0
 */
export declare const openapiV3: JsonSchemaDraft4;
/**
 * JSON Schema for OpenAPI Specification v3.1
 */
export declare const openapiV31: JsonSchemaDraft202012;
/**
 * JSON Schemas for every version of the OpenAPI Specification
 */
export declare const openapi: {
    v1: JsonSchemaDraft4;
    v2: JsonSchemaDraft4;
    v3: JsonSchemaDraft4;
    v31: JsonSchemaDraft202012;
};
export default openapi;
