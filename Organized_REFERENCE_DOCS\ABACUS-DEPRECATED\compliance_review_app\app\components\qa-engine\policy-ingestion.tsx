"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useDropzone } from "react-dropzone"
import { 
  AlertCircle, 
  Check, 
  FileText, 
  Loader2, 
  Plus, 
  Search, 
  Shield, 
  Upload, 
  X 
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface PolicyIngestionProps {
  projectId?: string
}

const POLICY_CATEGORIES = [
  { value: "general", label: "General FEMA Policies" },
  { value: "procurement", label: "Procurement Requirements" },
  { value: "environmental", label: "Environmental Compliance" },
  { value: "historic-preservation", label: "Historic Preservation" },
  { value: "insurance", label: "Insurance Requirements" },
  { value: "cost-eligibility", label: "Cost Eligibility" },
  { value: "documentation", label: "Documentation Requirements" },
  { value: "project-formulation", label: "Project Formulation" },
]

export function PolicyIngestion({ projectId }: PolicyIngestionProps) {
  const [policies, setPolicies] = useState<any[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newPolicy, setNewPolicy] = useState({
    title: "",
    description: "",
    category: "",
    content: "",
  })
  const { toast } = useToast()

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return

      setIsUploading(true)
      setUploadProgress(0)

      try {
        // Simulate file processing and policy extraction
        for (let i = 0; i <= 100; i += 10) {
          setUploadProgress(i)
          await new Promise(resolve => setTimeout(resolve, 200))
        }

        // Simulate policy extraction
        const extractedPolicies = acceptedFiles.map((file, index) => ({
          id: `policy-${Date.now()}-${index}`,
          title: file.name.replace(/\.[^/.]+$/, ""),
          description: `Extracted from ${file.name}`,
          category: POLICY_CATEGORIES[Math.floor(Math.random() * POLICY_CATEGORIES.length)].value,
          source: file.name,
          uploadedAt: new Date(),
          content: "This is a sample policy content extracted from the uploaded document. In a real implementation, this would contain the actual policy text extracted from the document.",
          status: "active",
        }))

        setPolicies(prev => [...prev, ...extractedPolicies])

        toast({
          title: "Policies ingested successfully",
          description: `${acceptedFiles.length} policy document(s) processed and added to the repository.`,
        })
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Policy ingestion failed",
          description: "There was an error processing the policy documents. Please try again.",
        })
      } finally {
        setIsUploading(false)
        setUploadProgress(0)
      }
    },
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const handleAddPolicy = () => {
    if (!newPolicy.title || !newPolicy.category) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide at least a title and category for the policy.",
      })
      return
    }

    const policy = {
      id: `policy-${Date.now()}`,
      ...newPolicy,
      source: "Manual Entry",
      uploadedAt: new Date(),
      status: "active",
    }

    setPolicies(prev => [...prev, policy])
    setShowAddDialog(false)
    setNewPolicy({
      title: "",
      description: "",
      category: "",
      content: "",
    })

    toast({
      title: "Policy added successfully",
      description: "The policy has been added to the repository.",
    })
  }

  const handleDeletePolicy = (id: string) => {
    setPolicies(prev => prev.filter(policy => policy.id !== id))
    
    toast({
      title: "Policy removed",
      description: "The policy has been removed from the repository.",
    })
  }

  const filteredPolicies = policies.filter(policy => 
    policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getCategoryLabel = (value: string) => {
    const category = POLICY_CATEGORIES.find(cat => cat.value === value)
    return category ? category.label : value
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Policy Repository
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage FEMA compliance policies for automated QA checks
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            onClick={() => setShowAddDialog(true)}
            className="flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Policy
          </Button>
        </div>
      </div>

      {/* Policy Ingestion Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Ingest Policy Documents
        </h3>
        
        <div 
          {...getRootProps()} 
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
            isDragActive ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-700'
          }`}
        >
          <input {...getInputProps()} />
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-base font-medium text-gray-900 dark:text-white">
            {isDragActive ? "Drop policy documents here..." : "Drag & drop policy documents here or click to browse"}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Supports PDF, Word, and text files (max 10MB each)
          </p>
        </div>

        {isUploading && (
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="flex items-center">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing policy documents...
              </span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="h-2" />
          </div>
        )}
      </div>

      {/* Policy List Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Policy Repository
          </h3>
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search policies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {filteredPolicies.length > 0 ? (
          <div className="space-y-4">
            {filteredPolicies.map((policy, index) => (
              <motion.div
                key={policy.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
              >
                <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-base font-medium text-gray-900 dark:text-white">
                        {policy.title}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {policy.description || "No description provided"}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                        {getCategoryLabel(policy.category)}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                        Source: {policy.source}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleDeletePolicy(policy.id)}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No policies found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {searchTerm ? "No policies match your search criteria" : "Start by adding policies or ingesting policy documents"}
            </p>
            {searchTerm && (
              <Button variant="outline" onClick={() => setSearchTerm("")}>
                Clear Search
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Add Policy Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Add New Policy</DialogTitle>
            <DialogDescription>
              Manually add a FEMA compliance policy to the repository
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Policy Title <span className="text-red-500">*</span>
              </label>
              <Input
                value={newPolicy.title}
                onChange={(e) => setNewPolicy({...newPolicy, title: e.target.value})}
                placeholder="Enter policy title"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Category <span className="text-red-500">*</span>
              </label>
              <Select
                value={newPolicy.category}
                onValueChange={(value) => setNewPolicy({...newPolicy, category: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {POLICY_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Description
              </label>
              <Input
                value={newPolicy.description}
                onChange={(e) => setNewPolicy({...newPolicy, description: e.target.value})}
                placeholder="Enter a brief description"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Policy Content
              </label>
              <Textarea
                value={newPolicy.content}
                onChange={(e) => setNewPolicy({...newPolicy, content: e.target.value})}
                placeholder="Enter the policy content or requirements"
                className="min-h-[150px]"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddPolicy}>
              Add Policy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}