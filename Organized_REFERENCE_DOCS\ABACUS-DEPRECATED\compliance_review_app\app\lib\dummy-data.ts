import { LucideIcon } from "lucide-react"

// Project Status Types
export type ProjectStatus = 'IN_PROGRESS' | 'UNDER_REVIEW' | 'COMPLETED' | 'REJECTED';
export type QAStatus = 'PENDING' | 'APPROVED' | 'REJECTED' | 'NEEDS_REVISION';

// Project Type
export interface Project {
  id: string;
  title: string;
  description: string | null;
  status: ProjectStatus;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  progress: number;
  documentCount: number;
  reviewerCount: number;
}

// User Type
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'USER' | 'ADMIN' | 'REVIEWER';
}

// Document Type
export interface Document {
  id: string;
  name: string;
  fileUrl: string;
  fileType: string;
  size: number;
  uploadedAt: Date;
  projectId: string;
  userId: string;
  stepId?: string;
  category?: string;
  stepName?: string;
}

// Compliance Step Type
export interface ComplianceStep {
  id: string;
  title: string;
  description: string | null;
  order: number;
  isCompleted: boolean;
  projectId: string;
}

// Question Type
export interface Question {
  id: string;
  text: string;
  answer: string | null;
  isRequired: boolean;
  stepId: string;
}

// QA Review Type
export interface QAReview {
  id: string;
  comments: string | null;
  status: QAStatus;
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  reviewerId: string;
  reviewer: {
    id: string;
    name: string;
    email: string;
  };
}

// Activity Type
export interface Activity {
  id: string;
  type: "document_upload" | "project_created" | "step_completed" | "review_submitted" | "user_added";
  description: string;
  timestamp: Date;
  projectId: string;
  projectTitle: string;
  userId?: string;
  userName?: string;
}

// Document Categories
export const DOCUMENT_CATEGORIES = [
  { value: "project-worksheet", label: "Project Worksheet" },
  { value: "damage-inventory", label: "Damage Inventory" },
  { value: "cost-documentation", label: "Cost Documentation" },
  { value: "procurement-documentation", label: "Procurement Documentation" },
  { value: "environmental-historic", label: "Environmental & Historic Preservation" },
  { value: "insurance-documentation", label: "Insurance Documentation" },
  { value: "permits", label: "Permits & Approvals" },
  { value: "correspondence", label: "Correspondence" },
  { value: "other", label: "Other" },
];

// Current User (Dummy)
export const currentUser: User = {
  id: "user-1",
  name: "John Doe",
  email: "<EMAIL>",
  role: "USER"
};

// Projects (Dummy)
export const projects: Project[] = [
  {
    id: "project-1",
    title: "Hurricane Relief Project",
    description: "FEMA Public Assistance project for hurricane damage recovery in coastal areas.",
    status: "IN_PROGRESS",
    createdAt: new Date(2023, 5, 15),
    updatedAt: new Date(2023, 6, 2),
    userId: "user-1",
    progress: 35,
    documentCount: 8,
    reviewerCount: 2
  },
  {
    id: "project-2",
    title: "Flood Mitigation Plan",
    description: "Comprehensive flood mitigation plan for riverside communities affected by spring flooding.",
    status: "UNDER_REVIEW",
    createdAt: new Date(2023, 4, 10),
    updatedAt: new Date(2023, 5, 28),
    userId: "user-1",
    progress: 75,
    documentCount: 12,
    reviewerCount: 3
  },
  {
    id: "project-3",
    title: "Wildfire Recovery Initiative",
    description: "Recovery project for public infrastructure damaged during the summer wildfires.",
    status: "COMPLETED",
    createdAt: new Date(2023, 2, 5),
    updatedAt: new Date(2023, 4, 15),
    userId: "user-1",
    progress: 100,
    documentCount: 15,
    reviewerCount: 2
  },
  {
    id: "project-4",
    title: "Tornado Damage Assessment",
    description: "Assessment and recovery plan for public buildings damaged by recent tornado activity.",
    status: "REJECTED",
    createdAt: new Date(2023, 3, 20),
    updatedAt: new Date(2023, 4, 5),
    userId: "user-1",
    progress: 60,
    documentCount: 6,
    reviewerCount: 1
  },
  {
    id: "project-5",
    title: "Emergency Response Coordination",
    description: "Coordination of emergency response resources for ongoing disaster relief efforts.",
    status: "IN_PROGRESS",
    createdAt: new Date(2023, 6, 1),
    updatedAt: new Date(2023, 6, 10),
    userId: "user-1",
    progress: 25,
    documentCount: 4,
    reviewerCount: 2
  },
  {
    id: "project-6",
    title: "Public Infrastructure Repair",
    description: "Repair and restoration of critical public infrastructure damaged during natural disasters.",
    status: "UNDER_REVIEW",
    createdAt: new Date(2023, 5, 5),
    updatedAt: new Date(2023, 6, 8),
    userId: "user-1",
    progress: 80,
    documentCount: 10,
    reviewerCount: 3
  }
];

// Get a specific project by ID
export function getProject(id: string): Project | undefined {
  return projects.find(project => project.id === id);
}

// Compliance Steps (Dummy)
export function getComplianceSteps(projectId: string): ComplianceStep[] {
  return [
    {
      id: "step-1",
      title: "Project Information",
      description: "Enter basic information about your FEMA Public Assistance project.",
      order: 1,
      isCompleted: true,
      projectId
    },
    {
      id: "step-2",
      title: "Eligibility Documentation",
      description: "Upload documents that establish eligibility for FEMA Public Assistance.",
      order: 2,
      isCompleted: projectId === "project-1" ? false : true,
      projectId
    },
    {
      id: "step-3",
      title: "Cost Documentation",
      description: "Provide documentation for all costs associated with the project.",
      order: 3,
      isCompleted: projectId === "project-1" || projectId === "project-2" ? false : true,
      projectId
    },
    {
      id: "step-4",
      title: "Environmental and Historic Preservation",
      description: "Address environmental and historic preservation requirements.",
      order: 4,
      isCompleted: projectId === "project-1" || projectId === "project-2" || projectId === "project-5" ? false : true,
      projectId
    },
    {
      id: "step-5",
      title: "Final Review",
      description: "Review all information before submission for quality assurance.",
      order: 5,
      isCompleted: projectId === "project-3" || projectId === "project-4" ? true : false,
      projectId
    }
  ];
}

// Questions (Dummy)
export function getQuestions(stepId: string): Question[] {
  if (stepId === "step-1") {
    return [
      {
        id: "question-1",
        text: "What is the disaster declaration number?",
        answer: stepId === "step-1" ? "DR-4611-FL" : null,
        isRequired: true,
        stepId
      },
      {
        id: "question-2",
        text: "What is the project worksheet (PW) number?",
        answer: stepId === "step-1" ? "PW-12345" : null,
        isRequired: true,
        stepId
      },
      {
        id: "question-3",
        text: "Describe the scope of work for this project.",
        answer: stepId === "step-1" ? "Repair and restoration of public buildings damaged during the hurricane." : null,
        isRequired: true,
        stepId
      }
    ];
  } else if (stepId === "step-3") {
    return [
      {
        id: "question-4",
        text: "Have all costs been documented with invoices, receipts, or other proof of payment?",
        answer: null,
        isRequired: true,
        stepId
      },
      {
        id: "question-5",
        text: "Are there any force account labor costs included in this project?",
        answer: null,
        isRequired: true,
        stepId
      }
    ];
  }
  
  return [];
}

// Documents (Dummy)
export function getDocuments(projectId: string): Document[] {
  const baseDocuments = [
    {
      id: "doc-1",
      name: "Damage Assessment Report.pdf",
      fileUrl: "/dummy-files/damage-assessment.pdf",
      fileType: "application/pdf",
      size: 2500000,
      uploadedAt: new Date(2023, 5, 16),
      projectId,
      userId: "user-1",
      stepId: "step-1",
      category: "damage-inventory",
      stepName: "Project Information"
    },
    {
      id: "doc-2",
      name: "Cost Estimate Worksheet.xlsx",
      fileUrl: "/dummy-files/cost-estimate.xlsx",
      fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      size: 1800000,
      uploadedAt: new Date(2023, 5, 18),
      projectId,
      userId: "user-1",
      stepId: "step-3",
      category: "cost-documentation",
      stepName: "Cost Documentation"
    },
    {
      id: "doc-3",
      name: "Project Photos.zip",
      fileUrl: "/dummy-files/project-photos.zip",
      fileType: "application/zip",
      size: 15000000,
      uploadedAt: new Date(2023, 5, 20),
      projectId,
      userId: "user-1",
      stepId: "step-2",
      category: "damage-inventory",
      stepName: "Eligibility Documentation"
    },
    {
      id: "doc-4",
      name: "Environmental Compliance Certificate.pdf",
      fileUrl: "/dummy-files/environmental-compliance.pdf",
      fileType: "application/pdf",
      size: 1200000,
      uploadedAt: new Date(2023, 5, 25),
      projectId,
      userId: "user-1",
      stepId: "step-4",
      category: "environmental-historic",
      stepName: "Environmental and Historic Preservation"
    },
    {
      id: "doc-5",
      name: "Procurement Documentation.docx",
      fileUrl: "/dummy-files/procurement-documentation.docx",
      fileType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      size: 950000,
      uploadedAt: new Date(2023, 5, 28),
      projectId,
      userId: "user-1",
      stepId: "step-3",
      category: "procurement-documentation",
      stepName: "Cost Documentation"
    }
  ];
  
  // Add more documents for specific projects
  if (projectId === "project-2") {
    baseDocuments.push(
      {
        id: "doc-6",
        name: "Flood Maps.jpg",
        fileUrl: "/dummy-files/flood-maps.jpg",
        fileType: "image/jpeg",
        size: 3500000,
        uploadedAt: new Date(2023, 5, 12),
        projectId,
        userId: "user-1",
        stepId: "step-2",
        category: "damage-inventory",
        stepName: "Eligibility Documentation"
      },
      {
        id: "doc-7",
        name: "FEMA Correspondence.pdf",
        fileUrl: "/dummy-files/fema-correspondence.pdf",
        fileType: "application/pdf",
        size: 750000,
        uploadedAt: new Date(2023, 5, 15),
        projectId,
        userId: "user-1",
        stepId: "step-1",
        category: "correspondence",
        stepName: "Project Information"
      }
    );
  }
  
  return baseDocuments;
}

// QA Reviews (Dummy)
export function getQAReviews(projectId: string): QAReview[] {
  const reviews: QAReview[] = [];
  
  if (projectId === "project-2") {
    reviews.push({
      id: "review-1",
      comments: "The project documentation is mostly complete, but we need additional information about the procurement process. Please provide the missing documentation.",
      status: "NEEDS_REVISION",
      createdAt: new Date(2023, 5, 30),
      updatedAt: new Date(2023, 5, 30),
      projectId,
      reviewerId: "user-2",
      reviewer: {
        id: "user-2",
        name: "Jane Smith",
        email: "<EMAIL>"
      }
    });
  } else if (projectId === "project-3") {
    reviews.push({
      id: "review-2",
      comments: "All documentation is complete and meets FEMA requirements. Project is approved for submission.",
      status: "APPROVED",
      createdAt: new Date(2023, 4, 10),
      updatedAt: new Date(2023, 4, 10),
      projectId,
      reviewerId: "user-3",
      reviewer: {
        id: "user-3",
        name: "Robert Johnson",
        email: "<EMAIL>"
      }
    });
  } else if (projectId === "project-4") {
    reviews.push({
      id: "review-3",
      comments: "The project does not meet FEMA eligibility requirements. Multiple critical documents are missing, and the scope of work is not clearly defined.",
      status: "REJECTED",
      createdAt: new Date(2023, 4, 5),
      updatedAt: new Date(2023, 4, 5),
      projectId,
      reviewerId: "user-2",
      reviewer: {
        id: "user-2",
        name: "Jane Smith",
        email: "<EMAIL>"
      }
    });
  }
  
  return reviews;
}

// Activities (Dummy)
export const activities: Activity[] = [
  {
    id: "activity-1",
    type: "project_created",
    description: "New project created: Hurricane Relief Project",
    timestamp: new Date(2023, 5, 15),
    projectId: "project-1",
    projectTitle: "Hurricane Relief Project"
  },
  {
    id: "activity-2",
    type: "document_upload",
    description: "Document uploaded: Damage Assessment Report.pdf",
    timestamp: new Date(2023, 5, 16),
    projectId: "project-1",
    projectTitle: "Hurricane Relief Project"
  },
  {
    id: "activity-3",
    type: "step_completed",
    description: "Completed step: Project Information",
    timestamp: new Date(2023, 5, 17),
    projectId: "project-1",
    projectTitle: "Hurricane Relief Project"
  },
  {
    id: "activity-4",
    type: "document_upload",
    description: "Document uploaded: Cost Estimate Worksheet.xlsx",
    timestamp: new Date(2023, 5, 18),
    projectId: "project-1",
    projectTitle: "Hurricane Relief Project"
  },
  {
    id: "activity-5",
    type: "review_submitted",
    description: "QA Review submitted with status: Needs Revision",
    timestamp: new Date(2023, 5, 30),
    projectId: "project-2",
    projectTitle: "Flood Mitigation Plan"
  },
  {
    id: "activity-6",
    type: "user_added",
    description: "User added to project: Jane Smith",
    timestamp: new Date(2023, 5, 25),
    projectId: "project-2",
    projectTitle: "Flood Mitigation Plan",
    userId: "user-2",
    userName: "Jane Smith"
  }
];

// Stats (Dummy)
export const stats = {
  totalProjects: projects.length,
  completedSteps: 12,
  documents: 18,
  teamMembers: 3
};

// Activity type to icon name mapping
export const activityIconMap = {
  "document_upload": "Upload",
  "project_created": "FileText",
  "step_completed": "CheckCircle2",
  "review_submitted": "Clock",
  "user_added": "UserPlus"
};

// Dashboard Stats Icons names
export const statsIconNames = {
  projects: "ClipboardCheck",
  steps: "CheckCircle2",
  documents: "FileText",
  teamMembers: "Users"
};