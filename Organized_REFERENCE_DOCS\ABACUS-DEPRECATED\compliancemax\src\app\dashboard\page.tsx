
import React from 'react';

export default function Dashboard() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">ComplianceMax Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Compliance Status</h2>
          <div className="flex items-center">
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mr-4">
              <span className="text-green-600 text-xl">85%</span>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">Overall compliance score</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Last updated: Today</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Active Projects</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>GDPR Implementation</span>
              <span className="text-yellow-500">In Progress</span>
            </div>
            <div className="flex justify-between">
              <span>ISO 27001 Certification</span>
              <span className="text-green-500">On Track</span>
            </div>
            <div className="flex justify-between">
              <span>HIPAA Compliance</span>
              <span className="text-red-500">Attention Needed</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Recent Documents</h2>
          <ul className="space-y-2">
            <li className="flex items-center">
              <span className="mr-2">📄</span>
              <span>Privacy Policy v2.1</span>
            </li>
            <li className="flex items-center">
              <span className="mr-2">📄</span>
              <span>Data Processing Agreement</span>
            </li>
            <li className="flex items-center">
              <span className="mr-2">📄</span>
              <span>Security Assessment Report</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
