# ComplianceMax

ComplianceMax is a comprehensive compliance management solution built with Next.js and Tailwind CSS. It helps organizations manage their compliance requirements, track projects, and maintain documentation.

## Features

- **Dashboard**: Get an overview of your compliance status, active projects, and recent documents
- **Projects**: Manage and track compliance projects with progress indicators, due dates, and priorities
- **Compliance Wizard**: Step-by-step guide to help determine applicable compliance requirements
- **Document Management**: Store, organize, and manage compliance-related documents

## Technology Stack

- **Frontend Framework**: Next.js 15.3.2
- **UI Framework**: Tailwind CSS 4
- **Language**: TypeScript
- **Styling**: Tailwind CSS with dark mode support
- **Fonts**: Geist Sans and Geist Mono

## Project Structure

```
compliancemax/
├── src/
│   ├── app/
│   │   ├── dashboard/
│   │   │   └── page.tsx
│   │   ├── projects/
│   │   │   └── page.tsx
│   │   ├── compliance-wizard/
│   │   │   └── page.tsx
│   │   ├── documents/
│   │   │   └── page.tsx
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
│       └── Navigation.tsx
├── public/
├── package.json
├── next.config.ts
└── tsconfig.json
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Run the development server:
   ```
   npm run dev
   ```
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Development

- The application uses the App Router in Next.js
- Each page is a React Server Component
- Navigation is handled through the Navigation component
- Styling is done with Tailwind CSS

## Future Enhancements

- User authentication and authorization
- Role-based access control
- API integration with compliance databases
- Document version control
- Automated compliance reporting
- Email notifications for compliance deadlines
