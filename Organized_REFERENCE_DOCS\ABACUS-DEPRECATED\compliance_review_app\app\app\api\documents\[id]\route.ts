import { NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = "force-dynamic"

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const document = await prisma.document.findUnique({
      where: {
        id: params.id,
      },
      include: {
        project: true,
      },
    })

    if (!document) {
      return NextResponse.json(
        { message: "Document not found" },
        { status: 404 }
      )
    }

    // Check if user has access to this document
    if (document.project.userId !== user.id && user.role !== "ADMIN" && user.role !== "REVIEWER") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    // In a real app, you would return the actual file or a signed URL
    // For this demo, we'll return a placeholder response
    return NextResponse.json({
      id: document.id,
      name: document.name,
      fileUrl: document.fileUrl,
      fileType: document.fileType,
      size: document.size,
      uploadedAt: document.uploadedAt,
    })
  } catch (error) {
    console.error("Error fetching document:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const document = await prisma.document.findUnique({
      where: {
        id: params.id,
      },
      include: {
        project: true,
      },
    })

    if (!document) {
      return NextResponse.json(
        { message: "Document not found" },
        { status: 404 }
      )
    }

    // Check if user has access to delete this document
    if (document.project.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    // Delete the document
    await prisma.document.delete({
      where: {
        id: params.id,
      },
    })

    return NextResponse.json({ message: "Document deleted successfully" })
  } catch (error) {
    console.error("Error deleting document:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}