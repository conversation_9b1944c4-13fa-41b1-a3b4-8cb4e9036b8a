"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Search, Filter, FileText, X } from "lucide-react"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { formatDate, formatBytes, getFileIcon } from "@/lib/utils"
import { DocumentPreview } from "./document-preview"

interface Document {
  id: string
  name: string
  fileUrl: string
  fileType: string
  size: number
  uploadedAt: Date
  category?: string
  stepId?: string | null
  stepName?: string
}

interface DocumentSearchProps {
  projectId: string
  documents: Document[]
}

const FEMA_DOCUMENT_CATEGORIES = [
  { value: "all", label: "All Categories" },
  { value: "project-worksheet", label: "Project Worksheet" },
  { value: "damage-inventory", label: "Damage Inventory" },
  { value: "cost-documentation", label: "Cost Documentation" },
  { value: "procurement-documentation", label: "Procurement Documentation" },
  { value: "environmental-historic", label: "Environmental & Historic Preservation" },
  { value: "insurance-documentation", label: "Insurance Documentation" },
  { value: "permits", label: "Permits & Approvals" },
  { value: "correspondence", label: "Correspondence" },
  { value: "other", label: "Other" },
]

export function DocumentSearch({ projectId, documents }: DocumentSearchProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>(documents)
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number>(-1)

  useEffect(() => {
    filterDocuments()
  }, [searchTerm, selectedCategory, documents])

  const filterDocuments = () => {
    let filtered = [...documents]
    
    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(doc => 
        doc.name.toLowerCase().includes(term) ||
        (doc.category && doc.category.toLowerCase().includes(term)) ||
        (doc.stepName && doc.stepName.toLowerCase().includes(term))
      )
    }
    
    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(doc => doc.category === selectedCategory)
    }
    
    setFilteredDocuments(filtered)
  }

  const handleDocumentClick = (document: Document, index: number) => {
    setSelectedDocument(document)
    setSelectedIndex(index)
  }

  const handleClosePreview = () => {
    setSelectedDocument(null)
    setSelectedIndex(-1)
  }

  const handleNextDocument = () => {
    if (selectedIndex < filteredDocuments.length - 1) {
      const nextIndex = selectedIndex + 1
      setSelectedDocument(filteredDocuments[nextIndex])
      setSelectedIndex(nextIndex)
    }
  }

  const handlePreviousDocument = () => {
    if (selectedIndex > 0) {
      const prevIndex = selectedIndex - 1
      setSelectedDocument(filteredDocuments[prevIndex])
      setSelectedIndex(prevIndex)
    }
  }

  const clearSearch = () => {
    setSearchTerm("")
    setSelectedCategory("all")
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Document Search
        </h3>
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchTerm && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          <div className="w-full sm:w-64">
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2 text-gray-400" />
                  <SelectValue placeholder="Filter by category" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {FEMA_DOCUMENT_CATEGORIES.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        {filteredDocuments.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredDocuments.map((document, index) => (
              <motion.div
                key={document.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                onClick={() => handleDocumentClick(document, index)}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                      {document.fileType ? (
                        getFileIcon(document.fileType)
                      ) : (
                        <FileText className="h-6 w-6 text-gray-500" />
                      )}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {document.name}
                        </h4>
                        <div className="mt-1 flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-500 dark:text-gray-400">
                          <span>Uploaded: {formatDate(document.uploadedAt)}</span>
                          <span>Size: {formatBytes(document.size)}</span>
                          {document.category && <span>Category: {document.category}</span>}
                        </div>
                      </div>
                      <div className="mt-2 sm:mt-0">
                        <Button size="sm" variant="ghost">
                          Preview
                        </Button>
                      </div>
                    </div>
                    {document.stepName && (
                      <div className="mt-2 text-xs">
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                          {document.stepName}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No documents found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || selectedCategory !== "all"
                ? "Try adjusting your search or filters"
                : "Upload documents to get started"}
            </p>
            {(searchTerm || selectedCategory !== "all") && (
              <Button variant="outline" onClick={clearSearch} className="mt-4">
                Clear Search
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Document Preview Modal */}
      {selectedDocument && (
        <DocumentPreview
          document={selectedDocument}
          onClose={handleClosePreview}
          onNext={handleNextDocument}
          onPrevious={handlePreviousDocument}
          hasNext={selectedIndex < filteredDocuments.length - 1}
          hasPrevious={selectedIndex > 0}
        />
      )}
    </div>
  )
}