"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  ChevronLeft, 
  ChevronRight, 
  Download, 
  FileText, 
  Maximize2, 
  Minimize2,
  X
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { formatDate } from "@/lib/utils"

interface Document {
  id: string
  name: string
  fileUrl: string
  fileType: string
  size: number
  uploadedAt: Date
  category?: string
}

interface DocumentPreviewProps {
  document: Document
  onClose: () => void
  onNext?: () => void
  onPrevious?: () => void
  hasNext?: boolean
  hasPrevious?: boolean
}

export function DocumentPreview({ 
  document, 
  onClose, 
  onNext, 
  onPrevious, 
  hasNext = false, 
  hasPrevious = false 
}: DocumentPreviewProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleDownload = () => {
    // In a real app, this would trigger a download of the actual file
    // For this demo, we'll just open the URL in a new tab
    window.open(document.fileUrl, '_blank')
  }

  const getPreviewContent = () => {
    const fileType = document.fileType.toLowerCase()
    
    // Show loading state
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      )
    }
    
    // For images
    if (fileType.includes('image')) {
      return (
        <img 
          src={document.fileUrl} 
          alt={document.name}
          className="max-w-full max-h-full object-contain"
          onLoad={() => setIsLoading(false)}
        />
      )
    }
    
    // For PDFs
    if (fileType.includes('pdf')) {
      return (
        <iframe 
          src={`${document.fileUrl}#toolbar=0`}
          className="w-full h-full border-0"
          onLoad={() => setIsLoading(false)}
        />
      )
    }
    
    // For other file types that can't be previewed directly
    return (
      <div className="flex flex-col items-center justify-center h-full p-8 text-center">
        <FileText className="h-16 w-16 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Preview not available
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          This file type cannot be previewed directly. Please download the file to view its contents.
        </p>
        <Button onClick={handleDownload}>
          <Download className="h-4 w-4 mr-2" />
          Download File
        </Button>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${
        isFullscreen ? 'p-0' : ''
      }`}
    >
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.9 }}
        className={`bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-hidden flex flex-col ${
          isFullscreen ? 'w-full h-full rounded-none' : 'max-w-4xl w-full max-h-[90vh]'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
            {document.name}
          </h3>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Document info */}
        <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span>Uploaded: {formatDate(document.uploadedAt)}</span>
            {document.category && <span>Category: {document.category}</span>}
          </div>
          <Button variant="ghost" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
        
        {/* Preview content */}
        <div className="flex-1 overflow-auto relative bg-gray-100 dark:bg-gray-800">
          {getPreviewContent()}
        </div>
        
        {/* Navigation controls */}
        {(hasPrevious || hasNext) && (
          <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onPrevious}
              disabled={!hasPrevious}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onNext}
              disabled={!hasNext}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}