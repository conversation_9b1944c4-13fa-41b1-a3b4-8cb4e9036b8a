"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  AlertCircle, 
  Bot, 
  CheckCircle, 
  FileText, 
  Loader2, 
  MessageSquare, 
  RefreshCw, 
  Send, 
  Sparkles 
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface OpenAIIntegrationProps {
  projectId: string
}

interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
}

interface ValidationResult {
  isCompliant: boolean
  confidence: number
  explanation: string
  suggestedActions?: string[]
  policyReferences?: string[]
}

export function OpenAIIntegration({ projectId }: OpenAIIntegrationProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "system-1",
      role: "system",
      content: "Welcome to the FEMA Compliance AI Assistant. I can help you validate compliance requirements, analyze documents, and provide guidance on FEMA policies.",
      timestamp: new Date(),
    }
  ])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<string>("")
  const [validationResults, setValidationResults] = useState<ValidationResult | null>(null)
  const { toast } = useToast()

  // Sample documents for demo
  const documents = [
    { id: "doc-1", name: "Procurement Policy.pdf" },
    { id: "doc-2", name: "Environmental Assessment.docx" },
    { id: "doc-3", name: "Cost Documentation.xlsx" },
    { id: "doc-4", name: "Insurance Certificate.pdf" },
    { id: "doc-5", name: "Project Worksheet.pdf" },
  ]

  const handleSendMessage = async () => {
    if (!input.trim()) return
    
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input,
      timestamp: new Date(),
    }
    
    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsLoading(true)
    
    try {
      // In a real app, this would call the OpenAI API
      // For this demo, we'll simulate a response
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: generateAIResponse(input),
        timestamp: new Date(),
      }
      
      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to get a response from the AI assistant.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const generateAIResponse = (query: string): string => {
    // Simple response generation for demo purposes
    const lowerQuery = query.toLowerCase()
    
    if (lowerQuery.includes("procurement") || lowerQuery.includes("contract")) {
      return "Based on FEMA procurement policies, all contracts must include specific federal provisions and follow competitive bidding processes unless there's a justified exception. Make sure your procurement documentation includes cost or price analysis and contractor selection criteria. For more details, refer to 2 CFR 200.318-326."
    }
    
    if (lowerQuery.includes("environmental") || lowerQuery.includes("historic")) {
      return "FEMA requires environmental and historic preservation reviews for most projects. This includes compliance with NEPA, the National Historic Preservation Act, and other federal laws. Documentation should include any required permits, consultation records, and assessment findings. For more information, see PAPPG V4 Chapter 8."
    }
    
    if (lowerQuery.includes("cost") || lowerQuery.includes("expense") || lowerQuery.includes("eligible")) {
      return "For costs to be eligible under FEMA Public Assistance, they must be: 1) directly tied to the performance of eligible work, 2) adequately documented, 3) reasonable and necessary, 4) compliant with federal regulations, and 5) not covered by another funding source. Review 2 CFR Part 200 for detailed cost principles."
    }
    
    if (lowerQuery.includes("insurance") || lowerQuery.includes("coverage")) {
      return "FEMA requires that insurable facilities damaged by the same hazard in a previous disaster must maintain insurance coverage to be eligible for assistance. Additionally, facilities in Special Flood Hazard Areas must maintain flood insurance. Documentation should include current policies and evidence of coverage. See Stafford Act Section 311 for requirements."
    }
    
    return "I understand you're asking about FEMA compliance. Could you provide more specific details about which aspect of compliance you're concerned with? I can help with procurement requirements, environmental compliance, cost eligibility, insurance requirements, or documentation standards."
  }

  const validateDocument = async () => {
    if (!selectedDocument) {
      toast({
        variant: "destructive",
        title: "No document selected",
        description: "Please select a document to validate.",
      })
      return
    }
    
    setIsLoading(true)
    setValidationResults(null)
    
    try {
      // In a real app, this would send the document to OpenAI for analysis
      // For this demo, we'll simulate a response
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Generate random validation results for demo
      const isCompliant = Math.random() > 0.3
      const confidence = Math.round((isCompliant ? 0.7 : 0.4) * 100 + Math.random() * 30)
      
      const results: ValidationResult = {
        isCompliant,
        confidence,
        explanation: isCompliant 
          ? "The document appears to meet FEMA compliance requirements. The content includes all necessary elements and follows the required format."
          : "The document may not fully comply with FEMA requirements. There are sections that need additional information or clarification.",
        suggestedActions: isCompliant 
          ? ["Ensure all signatures are included", "Verify dates are accurate", "Double-check all attachments are included"]
          : ["Add detailed cost breakdown", "Include procurement method justification", "Attach supporting documentation", "Clarify scope of work"],
        policyReferences: getRelevantPolicyReferences(selectedDocument),
      }
      
      setValidationResults(results)
      
      toast({
        title: "Document Validation Complete",
        description: `AI analysis of ${documents.find(d => d.id === selectedDocument)?.name} is complete.`,
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Validation failed",
        description: "There was an error validating the document.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getRelevantPolicyReferences = (documentId: string): string[] => {
    const references: Record<string, string[]> = {
      "doc-1": ["2 CFR 200.318-326", "PAPPG V4 Chapter 6", "FEMA Procurement Disaster Assistance Team Guide"],
      "doc-2": ["NEPA", "National Historic Preservation Act", "PAPPG V4 Chapter 8", "44 CFR Part 9"],
      "doc-3": ["2 CFR Part 200 Subpart E", "PAPPG V4 Chapter 7", "44 CFR 206.228"],
      "doc-4": ["Stafford Act Section 311", "44 CFR 206.252-253", "PAPPG V4 Chapter 6.5"],
      "doc-5": ["PAPPG V4 Chapter 3", "44 CFR 206.202", "FEMA Form 90-91"],
    }
    
    return references[documentId] || ["FEMA Policy Reference"]
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            AI-Powered Compliance
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Leverage OpenAI for advanced compliance validation and assistance
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Document Validation */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <Sparkles className="h-5 w-5 text-blue-500 mr-2" />
            AI Document Validation
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                Select Document to Validate
              </label>
              <Select
                value={selectedDocument}
                onValueChange={setSelectedDocument}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a document" />
                </SelectTrigger>
                <SelectContent>
                  {documents.map((doc) => (
                    <SelectItem key={doc.id} value={doc.id}>
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-blue-500" />
                        {doc.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              onClick={validateDocument} 
              disabled={!selectedDocument || isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing Document...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Validate with AI
                </>
              )}
            </Button>
            
            {validationResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
              >
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {validationResults.isCompliant ? (
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
                      )}
                      <h4 className="text-base font-medium text-gray-900 dark:text-white">
                        {validationResults.isCompliant ? "Compliant" : "Potential Compliance Issues"}
                      </h4>
                    </div>
                    <div className="text-sm font-medium">
                      <span className={`px-2.5 py-0.5 rounded-full text-xs ${
                        validationResults.confidence > 80 
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" 
                          : validationResults.confidence > 50
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      }`}>
                        {validationResults.confidence}% Confidence
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {validationResults.explanation}
                  </p>
                </div>
                
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Suggested Actions
                  </h5>
                  <ul className="space-y-1 text-sm text-gray-700 dark:text-gray-300">
                    {validationResults.suggestedActions?.map((action, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
                
                {validationResults.policyReferences && (
                  <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                    <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Policy References
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {validationResults.policyReferences.map((policy, index) => (
                        <span 
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                        >
                          {policy}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </div>

        {/* AI Assistant */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6 flex flex-col h-[600px]">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <Bot className="h-5 w-5 text-blue-500 mr-2" />
            Compliance AI Assistant
          </h3>
          
          <div className="flex-1 overflow-y-auto mb-4 space-y-4">
            {messages.map((message) => (
              <div 
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user' 
                    ? 'bg-blue-500 text-white' 
                    : message.role === 'system'
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                }`}>
                  <div className="flex items-center mb-1">
                    {message.role === 'user' ? (
                      <span className="text-xs font-medium">You</span>
                    ) : message.role === 'system' ? (
                      <span className="text-xs font-medium flex items-center">
                        <RefreshCw className="h-3 w-3 mr-1" />
                        System
                      </span>
                    ) : (
                      <span className="text-xs font-medium flex items-center">
                        <Bot className="h-3 w-3 mr-1" />
                        AI Assistant
                      </span>
                    )}
                    <span className="text-xs opacity-70 ml-2">
                      {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-gray-200 dark:bg-gray-700">
                  <div className="flex items-center">
                    <Bot className="h-4 w-4 mr-1 text-blue-500" />
                    <span className="text-xs font-medium">AI Assistant</span>
                  </div>
                  <div className="flex items-center mt-2 space-x-1">
                    <div className="h-2 w-2 rounded-full bg-gray-400 dark:bg-gray-500 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="h-2 w-2 rounded-full bg-gray-400 dark:bg-gray-500 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    <div className="h-2 w-2 rounded-full bg-gray-400 dark:bg-gray-500 animate-bounce" style={{ animationDelay: '600ms' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-end space-x-2">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask about FEMA compliance requirements..."
              className="flex-1 min-h-[80px] resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading}
              className="h-10 w-10 p-0 flex items-center justify-center"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}