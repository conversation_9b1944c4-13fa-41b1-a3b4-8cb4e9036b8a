import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getCurrentUser } from "@/lib/auth"

export const dynamic = "force-dynamic"

export async function GET() {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const projects = await prisma.project.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        updatedAt: "desc",
      },
    })

    return NextResponse.json(projects)
  } catch (error) {
    console.error("Error fetching projects:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const { title, description } = await request.json()

    // Create project
    const project = await prisma.project.create({
      data: {
        title,
        description,
        userId: user.id,
        // Create default compliance steps
        complianceSteps: {
          create: [
            {
              title: "Project Information",
              description: "Enter basic information about your FEMA Public Assistance project.",
              order: 1,
              questions: {
                create: [
                  {
                    text: "What is the disaster declaration number?",
                    isRequired: true,
                  },
                  {
                    text: "What is the project worksheet (PW) number?",
                    isRequired: true,
                  },
                  {
                    text: "Describe the scope of work for this project.",
                    isRequired: true,
                  },
                ],
              },
            },
            {
              title: "Eligibility Documentation",
              description: "Upload documents that establish eligibility for FEMA Public Assistance.",
              order: 2,
            },
            {
              title: "Cost Documentation",
              description: "Provide documentation for all costs associated with the project.",
              order: 3,
              questions: {
                create: [
                  {
                    text: "Have all costs been documented with invoices, receipts, or other proof of payment?",
                    isRequired: true,
                  },
                  {
                    text: "Are there any force account labor costs included in this project?",
                    isRequired: true,
                  },
                ],
              },
            },
            {
              title: "Environmental and Historic Preservation",
              description: "Address environmental and historic preservation requirements.",
              order: 4,
            },
            {
              title: "Final Review",
              description: "Review all information before submission for quality assurance.",
              order: 5,
            },
          ],
        },
      },
    })

    return NextResponse.json(project, { status: 201 })
  } catch (error) {
    console.error("Error creating project:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}