#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 FEMA Compliance Review App - Startup Check\n');

// Check Node.js version
console.log('📋 Checking system requirements...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ Node.js version 18 or higher is required. Current version:', nodeVersion);
  process.exit(1);
}
console.log('✅ Node.js version:', nodeVersion);

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Please run this from the project root.');
  process.exit(1);
}
console.log('✅ package.json found');

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies already installed');
}

// Check if .next directory exists and clean if needed
if (fs.existsSync('.next')) {
  console.log('🧹 Cleaning previous build...');
  try {
    execSync('rm -rf .next', { stdio: 'inherit' });
    console.log('✅ Previous build cleaned');
  } catch (error) {
    console.log('⚠️  Could not clean previous build, continuing...');
  }
}

// Check if image.png exists in public folder
const imagePath = path.join('public', 'image.png');
if (!fs.existsSync(imagePath)) {
  console.log('⚠️  image.png not found in public folder');
  console.log('   The app will still work, but the logo may not display correctly');
} else {
  console.log('✅ Logo image found');
}

// Type check
console.log('🔍 Running type check...');
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('✅ Type check passed');
} catch (error) {
  console.log('⚠️  Type check warnings (app will still run)');
}

// Build check
console.log('🏗️  Testing build...');
try {
  execSync('npm run build', { stdio: 'pipe' });
  console.log('✅ Build successful');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.log('🔧 Trying to fix common issues...');
  
  // Try to reinstall dependencies
  try {
    execSync('rm -rf node_modules package-lock.json && npm install', { stdio: 'inherit' });
    execSync('npm run build', { stdio: 'pipe' });
    console.log('✅ Build successful after dependency reinstall');
  } catch (retryError) {
    console.error('❌ Build still failing. Please check the error messages above.');
    process.exit(1);
  }
}

console.log('\n🎉 All checks passed! The app is ready to run.');
console.log('\n📋 Quick Start Commands:');
console.log('   npm run dev     - Start development server');
console.log('   npm run build   - Build for production');
console.log('   npm run start   - Start production server');
console.log('\n🌐 The app will be available at: http://localhost:3000');
console.log('\n✨ FEMA Compliance Review App is ready for use!');