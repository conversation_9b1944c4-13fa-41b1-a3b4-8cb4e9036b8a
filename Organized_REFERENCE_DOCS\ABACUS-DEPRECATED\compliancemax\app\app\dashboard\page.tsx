"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  FileText,
  ShieldAlert,
  ShieldCheck,
  Users,
  XCircle,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDateTime, formatCurrency, getStatusColor } from "@/lib/utils";
import {
  complianceStats,
  recentActivities,
  complianceTrends,
  fundingByCategory,
} from "@/lib/data";
import dynamic from "next/dynamic";
import ComplianceProgressTracker from "./components/compliance-progress-tracker";

// Dynamically import charts with SSR disabled
const LineChart = dynamic(
  () => import("react-chartjs-2").then((mod) => mod.Line),
  {
    ssr: false,
    loading: () => (
      <div className="flex h-[300px] items-center justify-center bg-muted/20 rounded-md">
        Loading compliance trends chart...
      </div>
    ),
  }
);

const BarChart = dynamic(
  () => import("react-chartjs-2").then((mod) => mod.Bar),
  {
    ssr: false,
    loading: () => (
      <div className="flex h-[300px] items-center justify-center bg-muted/20 rounded-md">
        Loading funding by category chart...
      </div>
    ),
  }
);

// Chart.js setup
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Sample compliance categories data for the tracker
const complianceCategories = [
  {
    id: "cat1",
    name: "Documentation Requirements",
    progress: 85,
    status: "complete" as const,
    items: [
      { id: "item1", name: "Damage Documentation", completed: true },
      { id: "item2", name: "Cost Documentation", completed: true },
      { id: "item3", name: "Procurement Documentation", completed: true },
      { id: "item4", name: "Environmental Documentation", completed: false },
    ],
  },
  {
    id: "cat2",
    name: "Eligibility Requirements",
    progress: 65,
    status: "in-progress" as const,
    items: [
      { id: "item5", name: "Applicant Eligibility", completed: true },
      { id: "item6", name: "Facility Eligibility", completed: true },
      { id: "item7", name: "Work Eligibility", completed: false },
      { id: "item8", name: "Cost Eligibility", completed: false },
    ],
  },
  {
    id: "cat3",
    name: "Procurement Compliance",
    progress: 40,
    status: "attention" as const,
    items: [
      { id: "item9", name: "Full and Open Competition", completed: true },
      { id: "item10", name: "Cost/Price Analysis", completed: false },
      { id: "item11", name: "Small/Minority Business Utilization", completed: false },
      { id: "item12", name: "Contract Provisions", completed: false },
    ],
  },
  {
    id: "cat4",
    name: "Environmental Compliance",
    progress: 75,
    status: "in-progress" as const,
    items: [
      { id: "item13", name: "NEPA Documentation", completed: true },
      { id: "item14", name: "Historic Preservation Review", completed: true },
      { id: "item15", name: "Wetlands Protection", completed: false },
      { id: "item16", name: "Endangered Species Act", completed: true },
    ],
  },
  {
    id: "cat5",
    name: "Insurance Requirements",
    progress: 100,
    status: "complete" as const,
    items: [
      { id: "item17", name: "Insurance Documentation", completed: true },
      { id: "item18", name: "Duplication of Benefits Analysis", completed: true },
      { id: "item19", name: "Obtain and Maintain Requirements", completed: true },
    ],
  },
];

export default function DashboardPage() {
  const [statsRef, statsInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [activityRef, activityInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [chartsRef, chartsInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [trackerRef, trackerInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  // Line chart data
  const lineChartData = {
    labels: complianceTrends.map((item) => item.month),
    datasets: [
      {
        label: "Approved",
        data: complianceTrends.map((item) => item.approved),
        borderColor: "rgb(34, 197, 94)",
        backgroundColor: "rgba(34, 197, 94, 0.5)",
        tension: 0.3,
      },
      {
        label: "Pending",
        data: complianceTrends.map((item) => item.pending),
        borderColor: "rgb(234, 179, 8)",
        backgroundColor: "rgba(234, 179, 8, 0.5)",
        tension: 0.3,
      },
      {
        label: "Rejected",
        data: complianceTrends.map((item) => item.rejected),
        borderColor: "rgb(239, 68, 68)",
        backgroundColor: "rgba(239, 68, 68, 0.5)",
        tension: 0.3,
      },
    ],
  };

  const lineChartOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Project Status Trends (6 Months)",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Bar chart data
  const barChartData = {
    labels: fundingByCategory.map((item) => item.category.split(' - ')[0]),
    datasets: [
      {
        label: "Approved Funding",
        data: fundingByCategory.map((item) => item.approved),
        backgroundColor: "rgba(34, 197, 94, 0.7)",
      },
      {
        label: "Pending Funding",
        data: fundingByCategory.map((item) => item.pending),
        backgroundColor: "rgba(234, 179, 8, 0.7)",
      },
      {
        label: "Rejected Funding",
        data: fundingByCategory.map((item) => item.rejected),
        backgroundColor: "rgba(239, 68, 68, 0.7)",
      },
    ],
  };

  const barChartOptions: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Funding by FEMA Category",
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', maximumFractionDigits: 0 }).format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', notation: 'compact', maximumFractionDigits: 1 }).format(Number(value));
          }
        }
      },
    },
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Monitor your FEMA Public Assistance compliance status and recent activities
        </p>
      </div>

      <motion.div
        ref={statsRef}
        initial={{ opacity: 0, y: 20 }}
        animate={statsInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
        className="grid gap-4 md:grid-cols-2 lg:grid-cols-4"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={statsInView ? { opacity: 1, scale: 1 } : {}}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Approved</CardTitle>
              <ShieldCheck className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success">
                {complianceStats.approved}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((complianceStats.approved / complianceStats.total) * 100)}% of total
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={statsInView ? { opacity: 1, scale: 1 } : {}}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <AlertTriangle className="h-5 w-5 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-warning">
                {complianceStats.pending}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((complianceStats.pending / complianceStats.total) * 100)}% of total
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={statsInView ? { opacity: 1, scale: 1 } : {}}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Rejected</CardTitle>
              <ShieldAlert className="h-5 w-5 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-destructive">
                {complianceStats.rejected}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((complianceStats.rejected / complianceStats.total) * 100)}% of total
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={statsInView ? { opacity: 1, scale: 1 } : {}}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
              <FileText className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{complianceStats.total}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Across all FEMA categories
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* Compliance Progress Tracker */}
      <motion.div
        ref={trackerRef}
        initial={{ opacity: 0, y: 20 }}
        animate={trackerInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
      >
        <ComplianceProgressTracker categories={complianceCategories} />
      </motion.div>

      <div className="grid gap-6 md:grid-cols-7">
        <motion.div
          ref={activityRef}
          initial={{ opacity: 0, x: -20 }}
          animate={activityInView ? { opacity: 1, x: 0 } : {}}
          transition={{ duration: 0.5 }}
          className="md:col-span-3"
        >
          <Card className="h-full hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {recentActivities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={activityInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    className="flex items-start gap-4"
                  >
                    <div className="relative mt-1">
                      <Avatar className="h-8 w-8 border-2 border-background">
                        <AvatarFallback>
                          {activity.user
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <span className="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-primary ring-2 ring-background" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{activity.action}</p>
                        <time className="text-xs text-muted-foreground">
                          {formatDateTime(activity.timestamp)}
                        </time>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        <span className="font-medium text-foreground">
                          {activity.user}
                        </span>{" "}
                        on project{" "}
                        <span className="font-medium text-foreground">
                          {activity.project}
                        </span>
                      </p>
                      {activity.status && (
                        <Badge variant={getStatusColor(activity.status) as any}>
                          {activity.status}
                        </Badge>
                      )}
                      {activity.document && (
                        <p className="text-xs">
                          Document: <span className="font-medium">{activity.document}</span>
                        </p>
                      )}
                      {activity.task && (
                        <p className="text-xs">
                          Task: <span className="font-medium">{activity.task}</span>
                        </p>
                      )}
                      {activity.result && (
                        <p className="text-xs">
                          Result: <span className="font-medium">{activity.result}</span>
                        </p>
                      )}
                      {activity.requirement && (
                        <p className="text-xs">
                          Requirement: <span className="font-medium">{activity.requirement}</span>
                        </p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          ref={chartsRef}
          initial={{ opacity: 0, x: 20 }}
          animate={chartsInView ? { opacity: 1, x: 0 } : {}}
          transition={{ duration: 0.5 }}
          className="md:col-span-4"
        >
          <Card className="h-full hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>FEMA Public Assistance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="trends">
                <TabsList className="mb-4">
                  <TabsTrigger value="trends">Status Trends</TabsTrigger>
                  <TabsTrigger value="funding">Funding by Category</TabsTrigger>
                </TabsList>
                <TabsContent value="trends" className="h-[300px]">
                  <LineChart data={lineChartData} options={lineChartOptions} />
                </TabsContent>
                <TabsContent value="funding" className="h-[300px]">
                  <BarChart data={barChartData} options={barChartOptions} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}