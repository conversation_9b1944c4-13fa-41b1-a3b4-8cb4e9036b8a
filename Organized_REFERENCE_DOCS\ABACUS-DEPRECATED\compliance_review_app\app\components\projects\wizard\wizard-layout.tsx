"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { CheckCircle2, ChevronLeft, ChevronRight, Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"

export interface WizardStep {
  id: string
  title: string
  description: string | null
  isCompleted: boolean
}

interface WizardLayoutProps {
  projectId: string
  currentStepId: string
  steps: WizardStep[]
  children: React.ReactNode
}

export function WizardLayout({ projectId, currentStepId, steps, children }: WizardLayoutProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const router = useRouter()

  const currentStepIndex = steps.findIndex(step => step.id === currentStepId)
  const currentStep = steps[currentStepIndex]
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1

  useEffect(() => {
    // Calculate progress percentage
    const completedSteps = steps.filter(step => step.isCompleted).length
    const progressPercentage = (completedSteps / steps.length) * 100
    setProgress(progressPercentage)
  }, [steps])

  const handleNext = async () => {
    setIsLoading(true)
    
    try {
      // Mark current step as completed
      await fetch(`/api/projects/${projectId}/steps/${currentStepId}/complete`, {
        method: "PUT",
      })
      
      // Navigate to next step if not the last one
      if (!isLastStep) {
        const nextStep = steps[currentStepIndex + 1]
        router.push(`/projects/${projectId}/wizard/${nextStep.id}`)
      } else {
        // If last step, redirect to project summary
        router.push(`/projects/${projectId}`)
      }
      
      router.refresh()
    } catch (error) {
      console.error("Failed to complete step:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = steps[currentStepIndex - 1]
      router.push(`/projects/${projectId}/wizard/${prevStep.id}`)
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white font-heading">
            Compliance Wizard
          </h2>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Step {currentStepIndex + 1} of {steps.length}
          </span>
        </div>
        <Progress value={progress} className="h-2" />
        
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`p-4 rounded-lg border ${
                step.id === currentStepId
                  ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20"
                  : step.isCompleted
                  ? "border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/20"
                  : "border-gray-200 dark:border-gray-700"
              } cursor-pointer transition-all duration-300 hover:shadow-soft`}
              onClick={() => {
                if (step.isCompleted || index <= currentStepIndex) {
                  router.push(`/projects/${projectId}/wizard/${step.id}`)
                }
              }}
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  {step.isCompleted ? (
                    <div className="h-6 w-6 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                  ) : (
                    <div className={`h-6 w-6 rounded-full flex items-center justify-center ${
                      step.id === currentStepId
                        ? "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                        : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                    }`}>
                      <span className="text-xs font-medium">{index + 1}</span>
                    </div>
                  )}
                </div>
                <div>
                  <h3 className={`text-sm font-medium ${
                    step.id === currentStepId
                      ? "text-blue-700 dark:text-blue-400"
                      : step.isCompleted
                      ? "text-green-700 dark:text-green-400"
                      : "text-gray-700 dark:text-gray-300"
                  } font-heading`}>
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                    {step.description || ""}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-medium border border-gray-200 dark:border-gray-700 p-6 mb-8"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-heading">
          {currentStep?.title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
          {currentStep?.description || ""}
        </p>
        
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          {children}
        </div>
      </motion.div>

      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={isFirstStep || isLoading}
          className="shadow-soft hover:shadow-medium transition-all"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button
          onClick={handleNext}
          disabled={isLoading}
          className="gradient-button"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {isLastStep ? "Completing" : "Saving"}
            </>
          ) : (
            <>
              {isLastStep ? "Complete" : "Next"}
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}