/**
 * ComplianceMax API Server
 * Express server integrating FEMA API client with database and frontend
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { Pool } = require('pg');
const FEMAApiClient = require('./utils/fema-api-client');
const GeographicService = require('./services/geographic-compliance');

const app = express();
const PORT = process.env.PORT || 5000;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/compliancemax_dev',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3333',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Root route - Enhanced ComplianceMax Dashboard
app.get('/', (req, res) => {
  res.status(200).send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ComplianceMax - Enterprise FEMA Compliance Management</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; min-height: 100vh; overflow-x: hidden;
            }
            .header { padding: 1rem 2rem; display: flex; justify-content: space-between; align-items: center; background: rgba(0,0,0,0.1); }
            .logo { font-size: 1.5rem; font-weight: bold; }
            .status-badge { background: #4CAF50; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; }
            .main { display: flex; align-items: center; justify-content: center; min-height: 90vh; padding: 2rem; }
            .container { text-align: center; max-width: 1000px; }
            h1 { font-size: 3.5rem; margin-bottom: 1rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); animation: fadeInUp 1s ease; }
            .subtitle { font-size: 1.4rem; margin-bottom: 3rem; opacity: 0.9; animation: fadeInUp 1s ease 0.2s both; }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem; margin: 3rem 0; }
            .feature { 
                background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 15px; 
                backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2);
                transition: all 0.3s ease; cursor: pointer; animation: fadeInUp 1s ease 0.4s both;
            }
            .feature:hover { transform: translateY(-5px); background: rgba(255,255,255,0.15); }
            .feature h3 { margin-bottom: 1rem; color: #ffd700; font-size: 1.2rem; }
            .feature p { opacity: 0.9; line-height: 1.6; }
            .cta-section { margin: 3rem 0; animation: fadeInUp 1s ease 0.6s both; }
            .cta-buttons { display: flex; flex-wrap: wrap; justify-content: center; gap: 1rem; margin: 2rem 0; }
            .btn { 
                display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem; 
                background: rgba(255,255,255,0.2); color: white; text-decoration: none; 
                border-radius: 30px; font-weight: 600; transition: all 0.3s ease;
                border: 2px solid rgba(255,255,255,0.3);
            }
            .btn:hover { 
                background: rgba(255,255,255,0.3); transform: translateY(-2px); 
                box-shadow: 0 10px 20px rgba(0,0,0,0.2); border-color: rgba(255,255,255,0.5);
            }
            .btn-primary { background: linear-gradient(45deg, #ff6b6b, #feca57); border: none; }
            .btn-primary:hover { background: linear-gradient(45deg, #ff5252, #ffc107); }
            .stats { display: flex; justify-content: center; gap: 2rem; margin: 2rem 0; flex-wrap: wrap; }
            .stat { text-align: center; }
            .stat-number { font-size: 2rem; font-weight: bold; color: #ffd700; }
            .stat-label { opacity: 0.8; font-size: 0.9rem; }
            .footer { text-align: center; padding: 2rem; opacity: 0.7; animation: fadeInUp 1s ease 0.8s both; }
            @keyframes fadeInUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
            @media (max-width: 768px) {
                h1 { font-size: 2.5rem; }
                .features { grid-template-columns: 1fr; }
                .cta-buttons { flex-direction: column; align-items: center; }
            }
        </style>
    </head>
    <body>
        <header class="header">
            <div class="logo">🏛️ ComplianceMax</div>
            <div class="status-badge">🟢 LIVE</div>
        </header>
        
        <main class="main">
            <div class="container">
                <h1>Enterprise FEMA Compliance Management</h1>
                <p class="subtitle">Revolutionary AI-powered system for disaster recovery compliance and appeals intelligence</p>
                
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">7,396</div>
                        <div class="stat-label">Compliance Items</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">AI Accuracy</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Real-time Monitoring</div>
                    </div>
                </div>
                
                <div class="features">
                    <div class="feature">
                        <h3>🚨 Real-time Disaster Intelligence</h3>
                        <p>Live FEMA disaster tracking with instant notifications and impact analysis for proactive compliance management</p>
                    </div>
                    <div class="feature">
                        <h3>🤖 Appeals Intelligence Engine</h3>
                        <p>AI-driven analysis of 50,000+ historical appeals to predict outcomes and optimize submission strategies</p>
                    </div>
                    <div class="feature">
                        <h3>📋 Smart Policy Matching</h3>
                        <p>Advanced document processing with ML-powered compliance verification across 11 categories and 13 topics</p>
                    </div>
                    <div class="feature">
                        <h3>🔗 Seamless FEMA Integration</h3>
                        <p>Direct OpenFEMA API connectivity with real-time data synchronization and automated validation</p>
                    </div>
                </div>
                
                <div class="cta-section">
                    <div class="cta-buttons">
                        <a href="/health" class="btn">📊 System Health</a>
                        <a href="/api/fema/disasters" class="btn">🌪️ Live Disasters</a>
                        <a href="/api/fema/disasters/enhanced" class="btn btn-primary">🗺️ Enhanced Geographic View</a>
                        <a href="/api/compliance/geographic/geojson" class="btn">📊 GeoJSON Data</a>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="footer">
            <p>🚀 Server: <strong>ACTIVE</strong> | Port: ${PORT} | Version: 1.0.0 | ${new Date().toLocaleDateString()}</p>
        </footer>
    </body>
    </html>
  `);
});

// Health check - Beautiful HTML response
app.get('/health', (req, res) => {
  res.status(200).send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ComplianceMax Health Check</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                color: white; min-height: 100vh; display: flex; align-items: center; justify-content: center;
            }
            .container { text-align: center; max-width: 600px; padding: 2rem; }
            h1 { font-size: 2.5rem; margin-bottom: 1rem; }
            .status { font-size: 1.5rem; margin: 1rem 0; }
            .details { background: rgba(255,255,255,0.1); padding: 1.5rem; border-radius: 10px; margin: 1rem 0; }
            .metric { display: inline-block; margin: 0.5rem 1rem; padding: 0.5rem; background: rgba(255,255,255,0.2); border-radius: 5px; }
            a { color: #ffeb3b; text-decoration: none; font-weight: bold; }
            a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>✅ System Healthy</h1>
            <div class="status">ComplianceMax API Server Running</div>
            <div class="details">
                <div class="metric">📊 Status: <strong>ACTIVE</strong></div>
                <div class="metric">🕒 Uptime: ${Math.floor(process.uptime())}s</div>
                <div class="metric">🚀 Version: 1.0.0</div>
                <div class="metric">📅 ${new Date().toLocaleString()}</div>
            </div>
            <p><a href="/">← Back to Dashboard</a></p>
        </div>
    </body>
    </html>
  `);
});

// Public Assistance Details Route
app.get('/api/fema/disaster/:drNumber/pa', async (req, res) => {
  try {
    const { drNumber } = req.params;
    const disasters = await FEMAApiClient.getCurrentDisasters();
    const disaster = disasters.find(d => d.drNumber === drNumber || d.disasterNumber === drNumber.replace('DR-', ''));
    
    if (!disaster) {
      return res.status(404).json({ success: false, error: 'Disaster not found' });
    }

    // Enhanced PA information
    const paDetails = {
      disaster: disaster,
      publicAssistance: {
        description: "State, local, tribal and territorial governments and certain private-non-profit organizations in these designated counties are eligible for assistance for emergency work and the repair or replacement of disaster-damaged facilities.",
        categories: {
          'PA': 'All categories of Public Assistance work',
          'PA-A': 'Debris Removal (Category A)',
          'PA-B': 'Emergency Protective Measures (Category B)', 
          'PA-C': 'Roads and Bridges (Category C)',
          'PA-D': 'Water Control Facilities (Category D)',
          'PA-E': 'Buildings and Equipment (Category E)',
          'PA-F': 'Utilities (Category F)',
          'PA-G': 'Parks, Recreation, and Other (Category G)'
        },
        eligibleCounties: disaster.counties || [],
        quickLinks: {
          stateAndLocal: '/recovery/state-local',
          national: '/recovery/national',
          socialMedia: '/connect/social',
          mobileApp: '/connect/mobile',
          helpline: 'tel:**************'
        }
      }
    };

    // Return JSON API response
    if (req.accepts('json') && !req.accepts('html')) {
      return res.json({ success: true, data: paDetails });
    }

    // Return HTML page
    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <title>${disaster.title} - Public Assistance | ComplianceMax</title>
          <style>
              * { margin: 0; padding: 0; box-sizing: border-box; }
              body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
              .header { background: #1e3a8a; color: white; padding: 1rem; }
              .container { max-width: 1000px; margin: 2rem auto; padding: 0 1rem; }
              .disaster-header { background: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              .disaster-title { font-size: 2rem; color: #1e3a8a; margin-bottom: 0.5rem; }
              .disaster-subtitle { color: #666; font-size: 1.1rem; }
              .pa-section { background: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              .section-title { font-size: 1.5rem; color: #1e3a8a; margin-bottom: 1rem; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem; }
              .pa-description { background: #f0f9ff; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; border-left: 4px solid #3b82f6; }
              .counties-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 0.5rem; margin: 1rem 0; }
              .county-item { background: #f3f4f6; padding: 0.5rem; border-radius: 5px; text-align: center; }
              .categories-list { display: grid; gap: 1rem; margin-top: 1rem; }
              .category-item { background: #f9fafb; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981; }
              .category-code { font-weight: bold; color: #065f46; }
              .quick-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem; }
              .quick-link { background: #3b82f6; color: white; padding: 1rem; text-decoration: none; border-radius: 8px; text-align: center; transition: background 0.3s; }
              .quick-link:hover { background: #2563eb; }
              .back-btn { background: #6b7280; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 5px; display: inline-block; margin-bottom: 1rem; }
          </style>
      </head>
      <body>
          <div class="header">
              <h1>🏛️ ComplianceMax - Public Assistance Details</h1>
          </div>
          
          <div class="container">
              <a href="/api/fema/disasters" class="back-btn">← Back to Disasters</a>
              
              <div class="disaster-header">
                  <div class="disaster-title">${disaster.title}</div>
                  <div class="disaster-subtitle">${disaster.drNumber} | ${disaster.state} | ${disaster.incidentType}</div>
                  <div>Declaration Date: ${new Date(disaster.declarationDate).toLocaleDateString()}</div>
                  <div>Status: <strong style="color: ${disaster.status === 'Active' ? '#ef4444' : '#6b7280'}">${disaster.status}</strong></div>
              </div>

              <div class="pa-section">
                  <div class="section-title">🏛️ Public Assistance</div>
                  <div class="pa-description">
                      ${paDetails.publicAssistance.description}
                  </div>
                  
                  <div class="section-title">📋 Assistance Categories</div>
                  <div class="categories-list">
                      ${Object.entries(paDetails.publicAssistance.categories).map(([code, desc]) => `
                          <div class="category-item">
                              <div class="category-code">${code}</div>
                              <div>${desc}</div>
                          </div>
                      `).join('')}
                  </div>
              </div>

              ${disaster.counties && disaster.counties.length > 0 ? `
                  <div class="pa-section">
                      <div class="section-title">📍 Eligible Counties (${disaster.counties.length})</div>
                      <div class="counties-grid">
                          ${disaster.counties.map(county => `<div class="county-item">${county} County</div>`).join('')}
                      </div>
                  </div>
              ` : ''}

              <div class="pa-section">
                  <div class="section-title">🔗 Quick Links</div>
                  <div class="quick-links">
                      <a href="#" class="quick-link">📊 State & Local Resources</a>
                      <a href="#" class="quick-link">🇺🇸 National Resources</a>
                      <a href="#" class="quick-link">📱 Mobile App & Text</a>
                      <a href="tel:**************" class="quick-link">📞 24/7 Disaster Distress Helpline</a>
                  </div>
              </div>
          </div>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error fetching PA details:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch PA details' });
  }
});

// FEMA API Routes
app.get('/api/fema/disasters', async (req, res) => {
  try {
    const disasters = await FEMAApiClient.getCurrentDisasters();
    
    // If request accepts JSON, return JSON
    if (req.accepts('json') && !req.accepts('html')) {
      return res.json({
        success: true,
        data: disasters,
        count: disasters.length
      });
    }
    
    // Otherwise return beautiful HTML page
    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Live FEMA Disasters - ComplianceMax</title>
          <style>
              * { margin: 0; padding: 0; box-sizing: border-box; }
              body { 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                  color: white; min-height: 100vh; padding: 2rem;
              }
              .header { text-align: center; margin-bottom: 2rem; }
              h1 { font-size: 2.5rem; margin-bottom: 1rem; }
              .stats { display: flex; justify-content: center; gap: 2rem; margin: 2rem 0; flex-wrap: wrap; }
              .stat { text-align: center; background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px; }
              .stat-number { font-size: 2rem; font-weight: bold; color: #ffd700; }
              .disasters-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1.5rem; max-width: 1200px; margin: 0 auto; }
              .disaster-card { 
                  background: rgba(255,255,255,0.1); border-radius: 15px; padding: 1.5rem;
                  backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
                  transition: transform 0.3s ease;
              }
                             .disaster-card:hover { transform: translateY(-5px); }
               .disaster-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem; }
               .disaster-title { font-size: 1.2rem; font-weight: bold; color: #ffd700; flex: 1; }
               .disaster-status { padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold; }
               .status-active { background: #ff6b6b; color: white; }
               .status-closed { background: #666; color: white; }
               .disaster-dr-number { font-size: 1.1rem; color: #fff; margin-bottom: 0.75rem; }
               .disaster-info { margin: 0.3rem 0; font-size: 0.95rem; }
               .disaster-counties { margin: 0.75rem 0; padding: 0.5rem; background: rgba(255,255,255,0.1); border-radius: 8px; }
               .counties-label { font-weight: bold; font-size: 0.9rem; margin-bottom: 0.25rem; }
               .counties-list { font-size: 0.85rem; opacity: 0.9; }
               .assistance-programs { margin: 0.75rem 0; }
               .programs-label { font-weight: bold; font-size: 0.9rem; margin-bottom: 0.5rem; }
               .programs-list { display: flex; flex-wrap: wrap; gap: 0.25rem; }
               .program-tag { background: #4CAF50; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.75rem; font-weight: bold; }
               .incident-period { margin-top: 0.75rem; padding-top: 0.5rem; border-top: 1px solid rgba(255,255,255,0.2); opacity: 0.8; font-size: 0.85rem; }
              .back-link { 
                  position: fixed; top: 1rem; left: 1rem; 
                  background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; 
                  border-radius: 25px; text-decoration: none; color: white;
              }
              .back-link:hover { background: rgba(255,255,255,0.3); }
              .empty-state { text-align: center; margin-top: 3rem; opacity: 0.8; }
          </style>
      </head>
      <body>
          <a href="/" class="back-link">← Dashboard</a>
          <div class="header">
              <h1>🌪️ Live FEMA Disasters</h1>
              <div style="text-align: center; margin: 1rem 0;">
                  <a href="/api/fema/disasters/enhanced" style="background: rgba(255,255,255,0.2); padding: 0.75rem 1.5rem; border-radius: 25px; text-decoration: none; color: white; font-weight: bold;">
                      🗺️ View Enhanced Geographic Analysis
                  </a>
              </div>
                             <div class="stats">
                   <div class="stat">
                       <div class="stat-number">${disasters.length}</div>
                       <div>Active Disasters</div>
                   </div>
                   <div class="stat">
                       <div class="stat-number">${disasters.filter(d => d.assistancePrograms && d.assistancePrograms.length > 0).length}</div>
                       <div>PA Eligible</div>
                   </div>
                   <div class="stat">
                       <div class="stat-number">${new Date().toLocaleDateString()}</div>
                       <div>Last Updated</div>
                   </div>
               </div>
          </div>
          
          <div class="disasters-grid">
              ${disasters.length > 0 ? disasters.slice(0, 12).map(disaster => `
                  <div class="disaster-card" onclick="window.location.href='/api/fema/disaster/${disaster.drNumber || 'DR-' + disaster.disasterNumber}/pa'" style="cursor: pointer;">
                      <div class="disaster-header">
                          <div class="disaster-title">${disaster.title || 'Disaster Declaration'}</div>
                          <div class="disaster-status ${disaster.status === 'Active' ? 'status-active' : 'status-closed'}">${disaster.status || 'Active'}</div>
                      </div>
                      <div class="disaster-dr-number"><strong>${disaster.drNumber || 'DR-' + disaster.disasterNumber}</strong></div>
                      <div class="disaster-info">📍 ${disaster.state || 'Unknown'}</div>
                      <div class="disaster-info">📅 Declared: ${disaster.declarationDate ? new Date(disaster.declarationDate).toLocaleDateString() : 'Date TBD'}</div>
                      <div class="disaster-info">🌪️ Type: ${disaster.incidentType || 'Not specified'}</div>
                      
                      ${disaster.counties && disaster.counties.length > 0 ? `
                          <div class="disaster-counties">
                              <div class="counties-label">📍 Affected Counties:</div>
                              <div class="counties-list">${disaster.counties.slice(0, 4).join(', ')}${disaster.counties.length > 4 ? ` + ${disaster.counties.length - 4} more` : ''}</div>
                          </div>
                      ` : ''}
                      
                      ${disaster.assistancePrograms && disaster.assistancePrograms.length > 0 ? `
                          <div class="assistance-programs">
                              <div class="programs-label">🏛️ Public Assistance:</div>
                              <div class="programs-list">
                                  ${disaster.assistancePrograms.map(program => `<span class="program-tag">${program}</span>`).join('')}
                              </div>
                          </div>
                      ` : ''}
                      
                      ${disaster.incidentBeginDate ? `
                          <div class="incident-period">
                              <small>Incident: ${new Date(disaster.incidentBeginDate).toLocaleDateString()}${disaster.incidentEndDate ? ' - ' + new Date(disaster.incidentEndDate).toLocaleDateString() : ' - Ongoing'}</small>
                          </div>
                      ` : ''}
                  </div>
              `).join('') : '<div class="empty-state"><h2>No active disasters found</h2><p>All systems operating normally</p></div>'}
          </div>
          
          <script>
              // Auto-refresh every 5 minutes
              setTimeout(() => location.reload(), 300000);
          </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('Error fetching disasters:', error);
    
    // Return error page for HTML requests
    if (req.accepts('html')) {
      return res.status(500).send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error - ComplianceMax</title>
            <style>
                body { font-family: Arial, sans-serif; background: #e74c3c; color: white; text-align: center; padding: 2rem; }
                .error { background: rgba(0,0,0,0.3); padding: 2rem; border-radius: 10px; display: inline-block; }
            </style>
        </head>
        <body>
            <div class="error">
                <h1>⚠️ Service Temporarily Unavailable</h1>
                <p>Unable to fetch disaster data. Please try again later.</p>
                <p><a href="/" style="color: #ffd700;">← Back to Dashboard</a></p>
            </div>
        </body>
        </html>
      `);
    }
    
    // JSON error response
    res.status(500).json({
      success: false,
      error: 'Failed to fetch disaster data',
      message: error.message
    });
  }
});

app.post('/api/fema/lookup-registration', async (req, res) => {
  try {
    const { femaNumber } = req.body;
    
    if (!femaNumber) {
      return res.status(400).json({
        success: false,
        error: 'FEMA number required'
      });
    }

    const validation = await FEMAApiClient.validateFEMANumber(femaNumber);
    const applicantData = await FEMAApiClient.autoPopulateApplicantData(femaNumber);
    
    res.json({
      success: true,
      validation,
      data: applicantData
    });
  } catch (error) {
    console.error('Error looking up FEMA registration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to lookup FEMA registration',
      message: error.message
    });
  }
});

app.post('/api/fema/appeals-recommendations', async (req, res) => {
  try {
    const { projectDetails } = req.body;
    
    const recommendations = await FEMAApiClient.getComplianceRecommendationsFromAppeals(projectDetails);
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    console.error('Error getting appeals recommendations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get appeals recommendations',
      message: error.message
    });
  }
});

// Authentication Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, name, organization, femaRegistration } = req.body;
    
    // Basic validation
    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        error: 'Email, password, and name are required'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
    }

    // FEMA registration validation if provided
    if (femaRegistration) {
      const femaRegex = /^FEMA-[A-Z0-9\-]+$/;
      if (!femaRegex.test(femaRegistration)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid FEMA registration format'
        });
      }
    }

    // In a real implementation, you would:
    // 1. Hash the password
    // 2. Check if user already exists
    // 3. Insert into database
    // 4. Generate JWT token
    
    // For now, return success with mock data
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: {
        id: Date.now(),
        email,
        name,
        organization,
        femaRegistration,
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error registering user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to register user',
      message: error.message
    });
  }
});

// Geographic Compliance Routes - SPECIFIC ROUTES FIRST!

// Geographic GeoJSON endpoint for mapping (MUST come before parameterized route)
app.get('/api/compliance/geographic/geojson', async (req, res) => {
  try {
    const disasters = await FEMAApiClient.getCurrentDisasters();
    const geoJSON = GeographicService.generateDisasterGeoJSON(disasters);
    
    res.setHeader('Content-Type', 'application/geo+json');
    res.json(geoJSON);
  } catch (error) {
    console.error('❌ Error generating GeoJSON:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to generate geographic data',
      message: error.message
    });
  }
});

// Individual disaster geographic analysis (parameterized route comes AFTER specific routes)
app.get('/api/compliance/geographic/:drNumber', async (req, res) => {
  try {
    const { drNumber } = req.params;
    const disasters = await FEMAApiClient.getCurrentDisasters();
    const disaster = disasters.find(d => d.drNumber === drNumber || d.disasterNumber.toString() === drNumber.replace('DR-', ''));
    
    if (!disaster) {
      return res.status(404).json({ 
        success: false, 
        error: 'Disaster not found',
        drNumber: drNumber 
      });
    }

    const geographicContext = GeographicService.getDisasterGeographicContext(disaster);
    
    res.json({
      success: true,
      disaster: disaster,
      geographic: geographicContext,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error getting geographic context:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve geographic compliance data',
      message: error.message
    });
  }
});

// Enhanced disasters endpoint with geographic data
app.get('/api/fema/disasters/enhanced', async (req, res) => {
  try {
    const disasters = await FEMAApiClient.getCurrentDisasters();
    
    const enhancedDisasters = disasters.map(disaster => {
      const geographic = GeographicService.getDisasterGeographicContext(disaster);
      return {
        ...disaster,
        geographic: geographic
      };
    });

    // If request accepts JSON, return JSON
    if (req.accepts('json') && !req.accepts('html')) {
      return res.json({
        success: true,
        data: enhancedDisasters,
        count: enhancedDisasters.length,
        generatedAt: new Date().toISOString()
      });
    }

    // Otherwise return enhanced HTML page
    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Enhanced FEMA Disasters with Geographic Data</title>
          <style>
              * { margin: 0; padding: 0; box-sizing: border-box; }
              body { 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                  color: white; min-height: 100vh; padding: 2rem;
              }
              .container { max-width: 1400px; margin: 0 auto; }
              h1 { text-align: center; margin-bottom: 2rem; font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
              .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
              .stat { background: rgba(255,255,255,0.1); padding: 1.5rem; border-radius: 10px; text-align: center; }
              .stat-number { font-size: 2rem; font-weight: bold; color: #3498db; }
              .disasters-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 1.5rem; }
              .disaster-card { 
                  background: rgba(255,255,255,0.05); 
                  border-radius: 15px; 
                  padding: 1.5rem; 
                  border: 1px solid rgba(255,255,255,0.1);
                  transition: transform 0.3s ease, box-shadow 0.3s ease;
              }
              .disaster-card:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0,0,0,0.3); }
              .disaster-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem; }
              .disaster-title { font-size: 1.2rem; font-weight: bold; color: #3498db; flex: 1; }
              .disaster-status { padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold; }
              .status-active { background: #e74c3c; color: white; }
              .status-closed { background: #95a5a6; color: white; }
              .disaster-info { margin: 0.5rem 0; display: flex; align-items: center; }
              .icon { margin-right: 0.5rem; }
              .geographic-section { margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.1); }
              .geographic-title { color: #f39c12; font-weight: bold; margin-bottom: 0.5rem; }
              .risk-badge { display: inline-block; padding: 0.25rem 0.5rem; border-radius: 10px; font-size: 0.7rem; margin-left: 0.5rem; }
              .risk-HIGH { background: #e74c3c; }
              .risk-MEDIUM { background: #f39c12; }
              .risk-LOW { background: #27ae60; }
              .counties-list { color: #bdc3c7; font-size: 0.9rem; }
              .nav-buttons { text-align: center; margin-bottom: 2rem; }
              .nav-button { 
                  background: #3498db; color: white; border: none; padding: 0.75rem 1.5rem; 
                  border-radius: 25px; margin: 0 0.5rem; text-decoration: none; 
                  display: inline-block; transition: all 0.3s ease;
              }
              .nav-button:hover { background: #2980b9; transform: translateY(-2px); }
          </style>
      </head>
      <body>
          <div class="container">
              <h1>🗺️ Enhanced FEMA Disasters with Geographic Analysis</h1>
              
              <div class="nav-buttons">
                  <a href="/" class="nav-button">🏠 Dashboard</a>
                  <a href="/api/fema/disasters" class="nav-button">📊 Basic View</a>
                  <a href="/api/compliance/geographic/geojson" class="nav-button">🗺️ GeoJSON Data</a>
                  <a href="/health" class="nav-button">❤️ Health Check</a>
              </div>

              <div class="stats">
                  <div class="stat">
                      <div class="stat-number">${enhancedDisasters.length}</div>
                      <div>Enhanced Disasters</div>
                  </div>
                  <div class="stat">
                      <div class="stat-number">${enhancedDisasters.filter(d => d.geographic.geographicRisk === 'HIGH').length}</div>
                      <div>High Risk Areas</div>
                  </div>
                  <div class="stat">
                      <div class="stat-number">${enhancedDisasters.reduce((sum, d) => sum + d.geographic.counties.length, 0)}</div>
                      <div>Total Counties</div>
                  </div>
                  <div class="stat">
                      <div class="stat-number">${new Date().toLocaleDateString()}</div>
                      <div>Analysis Date</div>
                  </div>
              </div>

              <div class="disasters-grid">
                  ${enhancedDisasters.map(disaster => `
                      <div class="disaster-card">
                          <div class="disaster-header">
                              <div class="disaster-title">${disaster.title || 'Disaster Declaration'}</div>
                              <div class="disaster-status ${disaster.status === 'Active' ? 'status-active' : 'status-closed'}">${disaster.status || 'Active'}</div>
                          </div>
                          
                          <div class="disaster-info">
                              <span class="icon">🆔</span>
                              <strong>${disaster.drNumber}</strong>
                          </div>
                          
                          <div class="disaster-info">
                              <span class="icon">📍</span>
                              ${disaster.state} (FIPS: ${disaster.geographic.stateFips || 'N/A'})
                          </div>
                          
                          <div class="disaster-info">
                              <span class="icon">📅</span>
                              Declared: ${disaster.declarationDate ? new Date(disaster.declarationDate).toLocaleDateString() : 'Date TBD'}
                          </div>
                          
                          <div class="disaster-info">
                              <span class="icon">🌪️</span>
                              Type: ${disaster.incidentType || 'Not specified'}
                          </div>
                          
                          <div class="disaster-info">
                              <span class="icon">🏛️</span>
                              Programs: ${(disaster.assistancePrograms || []).join(', ') || 'PA'}
                          </div>

                          <div class="geographic-section">
                              <div class="geographic-title">
                                  Geographic Risk Analysis
                                  <span class="risk-badge risk-${disaster.geographic.geographicRisk}">${disaster.geographic.geographicRisk}</span>
                              </div>
                              
                              <div class="disaster-info">
                                  <span class="icon">🏘️</span>
                                  Counties: ${disaster.geographic.counties.length}
                              </div>
                              
                              ${disaster.geographic.counties.length > 0 ? `
                                  <div class="counties-list">
                                      ${disaster.geographic.counties.map(c => `${c.name} (${c.fips})`).join(', ')}
                                  </div>
                              ` : ''}
                              
                              <div class="disaster-info">
                                  <span class="icon">⚠️</span>
                                  Compliance Zones: ${disaster.geographic.complianceZones.length}
                              </div>
                              
                              <div style="margin-top: 1rem;">
                                  <a href="/api/compliance/geographic/${disaster.drNumber}" class="nav-button" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                                      📊 Detailed Analysis
                                  </a>
                              </div>
                          </div>
                      </div>
                  `).join('')}
              </div>
          </div>
      </body>
      </html>
    `);
    
  } catch (error) {
    console.error('❌ Error fetching enhanced disasters:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch enhanced disaster data',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.path}`
  });
});

// Start server
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`🚀 ComplianceMax API Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔗 FEMA API integration: Ready`);
  });
}

// Export for testing
module.exports = app; 