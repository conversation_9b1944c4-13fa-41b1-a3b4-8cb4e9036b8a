"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { CheckCircle2, HelpCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import { Question } from "@/lib/dummy-data"

interface QuestionnaireProps {
  projectId: string
  stepId: string
  questions: Question[]
}

export function Questionnaire({ projectId, stepId, questions }: QuestionnaireProps) {
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()

  // Initialize answers from questions that already have answers
  useState(() => {
    const initialAnswers: Record<string, string> = {}
    questions.forEach(question => {
      if (question.answer) {
        initialAnswers[question.id] = question.answer
      }
    })
    setAnswers(initialAnswers)
  })

  const handleAnswerChange = (questionId: string, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  const saveAnswers = () => {
    setIsSaving(true)
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Answers saved",
        description: "Your responses have been saved successfully.",
      })
      setIsSaving(false)
    }, 1500)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white font-heading">
          Questionnaire
        </h3>
      </div>
      
      <div className="space-y-6">
        {questions.map((question, index) => (
          <motion.div
            key={question.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-start mb-3">
              <HelpCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {question.text}
                </p>
                {question.isRequired && (
                  <p className="text-xs text-red-500 mt-1">
                    * Required
                  </p>
                )}
              </div>
            </div>
            
            <div className="pl-7">
              {question.text.length > 100 ? (
                <Textarea
                  value={answers[question.id] || ""}
                  onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                  placeholder="Enter your answer here..."
                  className="resize-none min-h-[100px]"
                />
              ) : (
                <Input
                  value={answers[question.id] || ""}
                  onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                  placeholder="Enter your answer here..."
                />
              )}
            </div>
          </motion.div>
        ))}
      </div>
      
      <div className="flex justify-end">
        <Button
          onClick={saveAnswers}
          disabled={isSaving || questions.some(q => q.isRequired && !answers[q.id])}
          className="gradient-button"
        >
          {isSaving ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </div>
          ) : (
            <>
              <CheckCircle2 className="h-4 w-4 mr-2" />
              Save Answers
            </>
          )}
        </Button>
      </div>
    </div>
  )
}