import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getCurrentUser } from "@/lib/auth"

export const dynamic = "force-dynamic"

export async function PUT(
  request: Request,
  { params }: { params: { id: string; stepId: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const project = await prisma.project.findUnique({
      where: {
        id: params.id,
      },
    })

    if (!project) {
      return NextResponse.json(
        { message: "Project not found" },
        { status: 404 }
      )
    }

    if (project.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    const step = await prisma.complianceStep.findUnique({
      where: {
        id: params.stepId,
      },
    })

    if (!step) {
      return NextResponse.json(
        { message: "Step not found" },
        { status: 404 }
      )
    }

    const updatedStep = await prisma.complianceStep.update({
      where: {
        id: params.stepId,
      },
      data: {
        isCompleted: true,
      },
    })

    return NextResponse.json(updatedStep)
  } catch (error) {
    console.error("Error completing step:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}