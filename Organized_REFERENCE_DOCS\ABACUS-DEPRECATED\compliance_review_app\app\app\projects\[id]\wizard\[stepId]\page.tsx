"use client"

import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { ChevronLeft } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { WizardLayout } from "@/components/projects/wizard/wizard-layout"
import { DocumentUploadEnhanced } from "@/components/projects/document-management/document-upload-enhanced"
import { Questionnaire } from "@/components/projects/wizard/questionnaire"
import { getProject, getComplianceSteps, getQuestions } from "@/lib/dummy-data"

export default function WizardPage({ params }: { params: { id: string; stepId: string } }) {
  const router = useRouter()
  const project = getProject(params.id)
  
  if (!project) {
    return (
      <div className="container max-w-7xl mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 font-heading">
          Project Not Found
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          The project you are looking for does not exist or has been removed.
        </p>
        <Button asChild>
          <Link href="/projects">
            Back to Projects
          </Link>
        </Button>
      </div>
    )
  }
  
  const complianceSteps = getComplianceSteps(project.id)
  const currentStep = complianceSteps.find(step => step.id === params.stepId)
  
  if (!currentStep) {
    return (
      <div className="container max-w-7xl mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 font-heading">
          Step Not Found
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          The compliance step you are looking for does not exist or has been removed.
        </p>
        <Button asChild>
          <Link href={`/projects/${params.id}`}>
            Back to Project
          </Link>
        </Button>
      </div>
    )
  }
  
  const questions = getQuestions(params.stepId)

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <Button asChild variant="ghost" size="sm" className="-ml-3">
          <Link href={`/projects/${params.id}`}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Project
          </Link>
        </Button>
      </div>
      
      <WizardLayout
        projectId={params.id}
        currentStepId={params.stepId}
        steps={complianceSteps}
      >
        {/* Step Content */}
        <div className="space-y-8">
          {/* Enhanced Document Upload Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <DocumentUploadEnhanced 
              projectId={params.id} 
              stepId={params.stepId}
            />
          </motion.div>

          {/* Questionnaire Component (if step has questions) */}
          {questions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Questionnaire
                projectId={params.id}
                stepId={params.stepId}
                questions={questions}
              />
            </motion.div>
          )}
        </div>
      </WizardLayout>
    </div>
  )
}