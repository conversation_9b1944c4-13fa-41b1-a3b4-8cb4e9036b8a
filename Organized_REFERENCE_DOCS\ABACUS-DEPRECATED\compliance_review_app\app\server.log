
> app@0.1.0 dev
> next dev

   ▲ Next.js 15.3.2
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env

 ✓ Starting...
 ✓ Ready in 2.8s
 ○ Compiling / ...
[Error: Cannot apply unknown utility class: border-border]
 ✓ Compiled / in 8.3s (1652 modules)
 GET / 200 in 9076ms
 GET / 200 in 128ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 921ms (1642 modules)
 GET /hero-image.jpg 404 in 1333ms
 GET / 200 in 136ms
 GET /hero-image.jpg 404 in 72ms
 GET / 200 in 109ms
 GET / 200 in 119ms
 GET / 200 in 97ms
 GET /hero-image.jpg 404 in 88ms
 ⚠ Cross origin request detected from 4be47b997.preview.abacusai.app to /_next/* resource. In a future major version of Next.js, you will need to explicitly configure "allowedDevOrigins" in next.config to allow this.
Read more: https://nextjs.org/docs/app/api-reference/config/next-config-js/allowedDevOrigins
 ⚠ Cross origin request detected from 4be47b997-3000.preview.abacusai.app to /_next/* resource. In a future major version of Next.js, you will need to explicitly configure "allowedDevOrigins" in next.config to allow this.
Read more: https://nextjs.org/docs/app/api-reference/config/next-config-js/allowedDevOrigins
 GET / 200 in 114ms
 GET /hero-image.jpg 404 in 77ms
 ✓ Compiled in 788ms (1652 modules)
 GET / 200 in 168ms
 GET / 200 in 40ms
[Error: Cannot apply unknown utility class: border-border]
 ✓ Compiled in 863ms (1549 modules)
 ⨯ [Error: The default export is not a React Component in "/layout"] {
  page: '/'
}
 ⨯ [Error: The default export is not a React Component in "/layout"] {
  page: '/'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 3.1s (1883 modules)
 GET / 500 in 4373ms
 GET / 500 in 3744ms
 ⨯ [Error: The default export is not a React Component in "/layout"] {
  page: '/'
}
 GET / 500 in 809ms
 ✓ Compiled in 2.2s (1998 modules)
 GET / 200 in 1940ms
 GET / 200 in 2400ms
 GET / 200 in 313ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 1136ms (1983 modules)
 GET /hero-image.jpg 404 in 1697ms
 GET /hero-image.jpg 404 in 1199ms
 GET / 200 in 698ms
 GET / 200 in 1152ms
 GET /hero-image.jpg 404 in 84ms
 ✓ Compiled in 483ms (1961 modules)
 GET / 200 in 180ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 1873ms (1974 modules)
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '479410144'
}
 GET / 500 in 1288ms
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '479410144'
}
 GET /hero-image.jpg 500 in 450ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 1168ms (1993 modules)
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '499790843'
}
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '499790843'
}
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 1080ms
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '499790843'
}
 GET / 500 in 431ms
 ✓ Compiled in 578ms (1950 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 562ms
 GET /hero-image.jpg 404 in 97ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 112ms
 GET /hero-image.jpg 404 in 68ms
 ✓ Compiled in 732ms (1934 modules)
 GET / 200 in 228ms
 GET / 200 in 71ms
 ✓ Compiled in 1079ms (1950 modules)
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '2397739364'
}
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 597ms
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '2397739364'
}
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 449ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 3.3s (1950 modules)
 ⨯ [Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.] {
  digest: '1518938832'
}
 GET / 500 in 714ms
 ✓ Compiled in 523ms (1950 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 277ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 126ms
[Error: Cannot apply unknown utility class: border-border]
 ✓ Compiled in 3.7s (1960 modules)
 GET / 200 in 240ms
 GET / 200 in 113ms
[?25h
