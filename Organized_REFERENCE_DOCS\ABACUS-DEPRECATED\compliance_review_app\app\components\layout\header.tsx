"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { 
  ClipboardCheck, 
  FileText, 
  Home, 
  LogOut, 
  Menu, 
  Moon, 
  Plus, 
  Settings, 
  Sun, 
  User, 
  X 
} from "lucide-react"
import { useTheme } from "next-themes"

import { Button } from "@/components/ui/button"

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()

  useEffect(() => {
    setMounted(true)
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const isActive = (path: string) => {
    return pathname === path
  }

  if (!mounted) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="relative h-10 w-10 overflow-hidden bg-gray-200 dark:bg-gray-700 rounded">
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white font-heading">
                FEMA Compliance
              </span>
            </div>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-md"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <Link href="/" className="flex items-center space-x-2">
            <div className="relative h-10 w-10 overflow-hidden">
              <Image 
                src="/image.png" 
                alt="FEMA Compliance Logo" 
                fill 
                className="object-contain"
                priority
              />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white font-heading">
              FEMA Compliance
            </span>
          </Link>

          <nav className="hidden md:flex items-center space-x-1">
            <NavLink href="/dashboard" isActive={isActive("/dashboard")}>
              <Home className="h-4 w-4 mr-2" />
              Dashboard
            </NavLink>
            <NavLink href="/projects" isActive={isActive("/projects") || pathname.startsWith("/projects/")}>
              <FileText className="h-4 w-4 mr-2" />
              Projects
            </NavLink>
            
            <div className="ml-2 flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="rounded-full w-9 p-0"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              >
                {theme === "dark" ? (
                  <Sun className="h-4 w-4" />
                ) : (
                  <Moon className="h-4 w-4" />
                )}
              </Button>
              
              <Button asChild variant="default" size="sm" className="gradient-button">
                <Link href="/projects/new">
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Link>
              </Button>
            </div>
          </nav>

          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-1"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-white dark:bg-gray-900 shadow-lg"
          >
            <div className="px-4 py-3 space-y-1">
              <MobileNavLink
                href="/dashboard"
                isActive={isActive("/dashboard")}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Home className="h-5 w-5 mr-3" />
                Dashboard
              </MobileNavLink>
              <MobileNavLink
                href="/projects"
                isActive={isActive("/projects") || pathname.startsWith("/projects/")}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <FileText className="h-5 w-5 mr-3" />
                Projects
              </MobileNavLink>
              <MobileNavLink
                href="/projects/new"
                isActive={isActive("/projects/new")}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Plus className="h-5 w-5 mr-3" />
                New Project
              </MobileNavLink>
              <div className="pt-2 pb-1">
                <div className="flex items-center justify-between px-1 py-3">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Toggle theme
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-full w-9 p-0"
                    onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                  >
                    {theme === "dark" ? (
                      <Sun className="h-4 w-4" />
                    ) : (
                      <Moon className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}

interface NavLinkProps {
  href: string
  isActive: boolean
  children: React.ReactNode
}

function NavLink({ href, isActive, children }: NavLinkProps) {
  return (
    <Link
      href={href}
      className={`relative px-3 py-2 rounded-md text-sm font-medium transition-colors ${
        isActive
          ? "text-blue-600 dark:text-blue-400"
          : "text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
      }`}
    >
      <div className="flex items-center">
        {children}
      </div>
      {isActive && (
        <motion.div
          layoutId="activeIndicator"
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 dark:bg-blue-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </Link>
  )
}

interface MobileNavLinkProps {
  href: string
  isActive: boolean
  onClick: () => void
  children: React.ReactNode
  className?: string
}

function MobileNavLink({ href, isActive, onClick, children, className = "" }: MobileNavLinkProps) {
  return (
    <Link
      href={href}
      className={`flex items-center px-3 py-3 rounded-md text-sm font-medium transition-colors ${
        isActive
          ? "bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
          : "text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800/50"
      } ${className}`}
      onClick={onClick}
    >
      {children}
    </Link>
  )
}