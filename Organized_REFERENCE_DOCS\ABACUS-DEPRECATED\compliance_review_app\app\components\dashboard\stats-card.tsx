"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"

interface StatsCardProps {
  title: string
  value: number
  icon: React.ReactNode
  description: string
  index: number
}

export function StatsCard({ title, value, icon, description, index }: StatsCardProps) {
  const [count, setCount] = useState(0)
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  useEffect(() => {
    if (inView) {
      let start = 0
      const end = value
      const duration = 2000
      const increment = end / (duration / 16)
      
      const timer = setInterval(() => {
        start += increment
        if (start > end) {
          setCount(end)
          clearInterval(timer)
        } else {
          setCount(Math.floor(start))
        }
      }, 16)
      
      return () => clearInterval(timer)
    }
  }, [inView, value])

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-medium transition-all duration-300 card-hover-effect"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <h3 className="text-3xl font-bold mt-1 text-gray-900 dark:text-white font-heading">{count}</h3>
        </div>
        <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-full">
          {icon}
        </div>
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">{description}</p>
    </motion.div>
  )
}