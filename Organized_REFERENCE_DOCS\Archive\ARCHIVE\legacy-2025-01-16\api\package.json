{"name": "compliancemax-api", "version": "1.0.0", "description": "ComplianceMax API layer - Authentication and intelligent onboarding", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:api": "jest --testPathPattern=api", "test:coverage": "jest --coverage", "validate": "node scripts/validate-api.js", "docs": "swagger-jsdoc -d swaggerDef.js -o swagger.json ./routes/*.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.7.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "xml2js": "^0.6.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "eslint": "^8.49.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.0.3", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"], "collectCoverageFrom": ["routes/**/*.js", "middleware/**/*.js", "utils/**/*.js", "!**/node_modules/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}