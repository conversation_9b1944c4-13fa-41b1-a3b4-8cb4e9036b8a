"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Calendar,
  Download,
  FileText,
  Filter,
  FolderOpen,
  Plus,
  Search,
  Tag,
  Upload,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils";
import { documents, documentCategories, documentTags } from "@/lib/data";

export default function DocumentsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [tagFilter, setTagFilter] = useState("all");
  
  const [headerRef, headerInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [filtersRef, filtersInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [documentsRef, documentsInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  // Filter documents
  const filteredDocuments = documents.filter((document) => {
    // Search filter
    const matchesSearch =
      searchQuery === "" ||
      document.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      document.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Category filter
    const matchesCategory =
      categoryFilter === "all" || document.category === categoryFilter;

    // Tag filter
    const matchesTag =
      tagFilter === "all" || document.tags.includes(tagFilter);

    return matchesSearch && matchesCategory && matchesTag;
  });

  // Get document icon based on type
  const getDocumentIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="h-6 w-6 text-red-500" />;
      case "docx":
        return <FileText className="h-6 w-6 text-blue-500" />;
      case "xlsx":
        return <FileText className="h-6 w-6 text-green-500" />;
      case "pptx":
        return <FileText className="h-6 w-6 text-orange-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-8">
      <motion.div
        ref={headerRef}
        initial={{ opacity: 0, y: -20 }}
        animate={headerInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Documents</h1>
          <p className="text-muted-foreground mt-2">
            Manage and organize your FEMA Public Assistance documentation
          </p>
        </div>
        <Button className="gap-2">
          <Upload className="h-4 w-4" />
          Upload Document
        </Button>
      </motion.div>

      <motion.div
        ref={filtersRef}
        initial={{ opacity: 0, y: 20 }}
        animate={filtersInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row gap-4"
      >
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search documents..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select
              value={categoryFilter}
              onValueChange={setCategoryFilter}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {documentCategories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Select
            value={tagFilter}
            onValueChange={setTagFilter}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Tag" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tags</SelectItem>
              {documentTags.map((tag) => (
                <SelectItem key={tag.id} value={tag.name}>
                  {tag.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </motion.div>

      <motion.div
        ref={documentsRef}
        initial={{ opacity: 0, y: 20 }}
        animate={documentsInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
      >
        <Tabs defaultValue="grid" className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="grid">Grid View</TabsTrigger>
              <TabsTrigger value="list">List View</TabsTrigger>
            </TabsList>
            <div className="text-sm text-muted-foreground">
              {filteredDocuments.length} documents
            </div>
          </div>

          <TabsContent value="grid" className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredDocuments.length === 0 ? (
                <div className="col-span-full text-center py-8 text-muted-foreground">
                  No documents match your filters
                </div>
              ) : (
                filteredDocuments.map((document, index) => (
                  <motion.div
                    key={document.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={documentsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.3, delay: 0.05 * (index % 6) }}
                  >
                    <Card className="hover:shadow-md transition-shadow h-full">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className="flex h-12 w-12 items-center justify-center rounded-md bg-primary/10">
                            {getDocumentIcon(document.type)}
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">{document.name}</h3>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {document.description}
                            </p>
                            <div className="flex flex-wrap gap-1 pt-1">
                              {document.tags.slice(0, 2).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {document.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{document.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center justify-between text-xs text-muted-foreground pt-2">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(document.uploadDate)}</span>
                              </div>
                              <div>{document.size}</div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="list" className="space-y-4">
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {filteredDocuments.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No documents match your filters
                    </div>
                  ) : (
                    filteredDocuments.map((document, index) => (
                      <motion.div
                        key={document.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={documentsInView ? { opacity: 1, y: 0 } : {}}
                        transition={{ duration: 0.3, delay: 0.05 * index }}
                      >
                        <div className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-primary/10">
                              {getDocumentIcon(document.type)}
                            </div>
                            <div>
                              <div className="font-medium">{document.name}</div>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <FolderOpen className="h-3 w-3" />
                                <span>{document.category}</span>
                                <span>•</span>
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(document.uploadDate)}</span>
                                <span>•</span>
                                <span>{document.size}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex gap-1">
                              {document.tags.slice(0, 2).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {document.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{document.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}