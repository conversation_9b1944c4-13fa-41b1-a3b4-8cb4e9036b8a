"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  AlertCircle, 
  CheckCircle, 
  ChevronDown, 
  ChevronRight, 
  Clock, 
  FileText, 
  Loader2, 
  Play, 
  Shield, 
  XCircle 
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface AutomatedQAProps {
  projectId: string
}

interface QAFinding {
  id: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  status: 'open' | 'resolved' | 'exception'
  documentReference?: string
  policyReference?: string
  resolutionSteps?: string
}

interface QACheck {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'passed' | 'failed' | 'warning'
  category: string
  findings?: QAFinding[]
  policyReferences?: string[]
  progress?: number
}

export function AutomatedQA({ projectId }: AutomatedQAProps) {
  const [qaChecks, setQAChecks] = useState<QACheck[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [overallProgress, setOverallProgress] = useState(0)
  const [expandedFindings, setExpandedFindings] = useState<string[]>([])
  const { toast } = useToast()

  // Initialize with sample QA checks
  useEffect(() => {
    setQAChecks([
      {
        id: "qa-1",
        title: "Procurement Documentation Compliance",
        description: "Validates that procurement documentation meets FEMA requirements",
        status: "pending",
        category: "procurement",
        policyReferences: ["2 CFR 200.318-326", "PAPPG V4 Chapter 6"],
      },
      {
        id: "qa-2",
        title: "Environmental Compliance",
        description: "Checks for required environmental documentation and permits",
        status: "pending",
        category: "environmental",
        policyReferences: ["NEPA", "PAPPG V4 Chapter 8"],
      },
      {
        id: "qa-3",
        title: "Cost Eligibility Analysis",
        description: "Validates that all costs are eligible under FEMA guidelines",
        status: "pending",
        category: "cost-eligibility",
        policyReferences: ["2 CFR Part 200", "PAPPG V4 Chapter 7"],
      },
      {
        id: "qa-4",
        title: "Insurance Requirements Verification",
        description: "Ensures that insurance requirements are met",
        status: "pending",
        category: "insurance",
        policyReferences: ["Stafford Act Section 311", "PAPPG V4 Chapter 6"],
      },
      {
        id: "qa-5",
        title: "Documentation Completeness",
        description: "Verifies that all required documentation is present",
        status: "pending",
        category: "documentation",
        policyReferences: ["PAPPG V4 Chapter 3"],
      },
    ])
  }, [])

  const runAutomatedQA = async () => {
    if (isRunning) return
    
    setIsRunning(true)
    setOverallProgress(0)
    
    // Reset all checks to pending
    setQAChecks(prev => prev.map(check => ({
      ...check,
      status: "pending",
      findings: undefined,
      progress: 0,
    })))
    
    // Simulate running each check sequentially
    for (let i = 0; i < qaChecks.length; i++) {
      const checkId = qaChecks[i].id
      
      // Set check to running
      setQAChecks(prev => prev.map(check => 
        check.id === checkId ? { ...check, status: "running", progress: 0 } : check
      ))
      
      // Simulate progress updates
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        
        setQAChecks(prev => prev.map(check => 
          check.id === checkId ? { ...check, progress } : check
        ))
        
        // Update overall progress
        const overallPercent = ((i * 100) + progress) / qaChecks.length
        setOverallProgress(overallPercent)
      }
      
      // Generate random findings
      const findings = generateRandomFindings(checkId)
      const status = findings.some(f => f.severity === 'critical' || f.severity === 'high') 
        ? 'failed' 
        : findings.some(f => f.severity === 'medium') 
          ? 'warning' 
          : 'passed'
      
      // Update check with results
      setQAChecks(prev => prev.map(check => 
        check.id === checkId ? { 
          ...check, 
          status, 
          findings,
          progress: 100,
        } : check
      ))
    }
    
    setIsRunning(false)
    setOverallProgress(100)
    
    toast({
      title: "QA Process Complete",
      description: "Automated compliance checks have been completed.",
    })
  }

  const generateRandomFindings = (checkId: string): QAFinding[] => {
    // For demo purposes, generate random findings
    const numFindings = Math.floor(Math.random() * 4) // 0-3 findings
    
    if (numFindings === 0) return []
    
    const severities: ('critical' | 'high' | 'medium' | 'low')[] = ['critical', 'high', 'medium', 'low']
    const findings: QAFinding[] = []
    
    for (let i = 0; i < numFindings; i++) {
      const severity = severities[Math.floor(Math.random() * severities.length)]
      
      findings.push({
        id: `finding-${checkId}-${i}`,
        description: getRandomFindingDescription(checkId),
        severity,
        status: 'open',
        documentReference: `Document-${Math.floor(Math.random() * 10) + 1}`,
        policyReference: getRandomPolicyReference(checkId),
        resolutionSteps: "Review the referenced document and ensure it complies with the policy requirements.",
      })
    }
    
    return findings
  }

  const getRandomFindingDescription = (checkId: string): string => {
    const descriptions: Record<string, string[]> = {
      "qa-1": [
        "Missing competitive bidding documentation",
        "Contract does not include required federal provisions",
        "No cost or price analysis for procurement",
        "Inadequate contractor selection documentation",
      ],
      "qa-2": [
        "Missing environmental impact assessment",
        "Required permits not documented",
        "No evidence of historic preservation review",
        "Wetland impact not properly addressed",
      ],
      "qa-3": [
        "Costs not adequately documented",
        "Ineligible costs included in project",
        "Duplicate costs identified",
        "Costs outside of approved scope of work",
      ],
      "qa-4": [
        "Insurance coverage does not meet requirements",
        "No evidence of flood insurance for facility in SFHA",
        "Insurance proceeds not properly accounted for",
        "Missing insurance policy documentation",
      ],
      "qa-5": [
        "Missing damage inventory documentation",
        "Incomplete project description",
        "Scope of work lacks sufficient detail",
        "Missing photographs of damaged facilities",
      ],
    }
    
    const options = descriptions[checkId] || ["Compliance issue identified"]
    return options[Math.floor(Math.random() * options.length)]
  }

  const getRandomPolicyReference = (checkId: string): string => {
    const references: Record<string, string[]> = {
      "qa-1": ["2 CFR 200.320", "2 CFR 200.323", "PAPPG V4 p.30-32"],
      "qa-2": ["NEPA Section 106", "44 CFR Part 9", "PAPPG V4 Chapter 8.2"],
      "qa-3": ["2 CFR 200.403", "2 CFR 200.404", "PAPPG V4 p.21-23"],
      "qa-4": ["44 CFR 206.252", "44 CFR 206.253", "PAPPG V4 p.97-99"],
      "qa-5": ["PAPPG V4 Appendix B", "PAPPG V4 Chapter 3.2", "44 CFR 206.202"],
    }
    
    const options = references[checkId] || ["FEMA Policy Reference"]
    return options[Math.floor(Math.random() * options.length)]
  }

  const toggleFinding = (findingId: string) => {
    setExpandedFindings(prev => 
      prev.includes(findingId) 
        ? prev.filter(id => id !== findingId)
        : [...prev, findingId]
    )
  }

  const markFindingAsResolved = (findingId: string) => {
    setQAChecks(prev => prev.map(check => {
      if (!check.findings) return check
      
      const updatedFindings = check.findings.map(finding => 
        finding.id === findingId ? { ...finding, status: 'resolved' as const } : finding
      )
      
      // Update check status if all findings are resolved
      const allResolved = updatedFindings.every(f => f.status !== 'open')
      const hasCritical = updatedFindings.some(f => f.severity === 'critical' && f.status === 'open')
      const hasHigh = updatedFindings.some(f => f.severity === 'high' && f.status === 'open')
      const hasMedium = updatedFindings.some(f => f.severity === 'medium' && f.status === 'open')
      
      let status = check.status
      if (allResolved) {
        status = 'passed'
      } else if (!hasCritical && !hasHigh && hasMedium) {
        status = 'warning'
      }
      
      return {
        ...check,
        findings: updatedFindings,
        status,
      }
    }))
    
    toast({
      title: "Finding Resolved",
      description: "The compliance finding has been marked as resolved.",
    })
  }

  const markFindingAsException = (findingId: string) => {
    setQAChecks(prev => prev.map(check => {
      if (!check.findings) return check
      
      const updatedFindings = check.findings.map(finding => 
        finding.id === findingId ? { ...finding, status: 'exception' as const } : finding
      )
      
      // Update check status
      const openFindings = updatedFindings.filter(f => f.status === 'open')
      const hasCritical = openFindings.some(f => f.severity === 'critical')
      const hasHigh = openFindings.some(f => f.severity === 'high')
      const hasMedium = openFindings.some(f => f.severity === 'medium')
      
      let status = check.status
      if (openFindings.length === 0) {
        status = 'passed'
      } else if (!hasCritical && !hasHigh && hasMedium) {
        status = 'warning'
      }
      
      return {
        ...check,
        findings: updatedFindings,
        status,
      }
    }))
    
    toast({
      title: "Exception Recorded",
      description: "The compliance finding has been marked as an exception.",
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-gray-400" />
      case 'running':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
      case 'passed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
      case 'running':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
      case 'pending':
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getFindingStatusColor = (status: string) => {
    switch (status) {
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
      case 'exception':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
      case 'open':
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Automated QA
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Run automated compliance checks against FEMA policies
          </p>
        </div>
        <Button 
          onClick={runAutomatedQA} 
          disabled={isRunning}
          className="flex items-center"
        >
          {isRunning ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Running QA Checks...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Run QA Checks
            </>
          )}
        </Button>
      </div>

      {/* Overall Progress */}
      {(isRunning || overallProgress > 0) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              QA Progress
            </h3>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {Math.round(overallProgress)}%
            </span>
          </div>
          <Progress value={overallProgress} className="h-2" />
          
          <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
            {qaChecks.map((check) => (
              <div 
                key={check.id}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center p-2 rounded-full bg-gray-100 dark:bg-gray-700">
                  {check.status === 'running' ? (
                    <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                  ) : check.status === 'pending' ? (
                    <Clock className="h-5 w-5 text-gray-400" />
                  ) : check.status === 'passed' ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : check.status === 'warning' ? (
                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 truncate">
                  {check.title}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* QA Checks */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Compliance Checks
        </h3>
        
        <Accordion type="single" collapsible className="space-y-4">
          {qaChecks.map((check) => (
            <AccordionItem 
              key={check.id} 
              value={check.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                <div className="flex items-center space-x-3 text-left">
                  <div className="flex-shrink-0">
                    {getStatusIcon(check.status)}
                  </div>
                  <div>
                    <h4 className="text-base font-medium text-gray-900 dark:text-white">
                      {check.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {check.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {check.status !== 'pending' && check.status !== 'running' && (
                    <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(check.status)}`}>
                      {check.status.charAt(0).toUpperCase() + check.status.slice(1)}
                    </span>
                  )}
                  {check.status === 'running' && check.progress !== undefined && (
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {check.progress}%
                    </span>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
                {/* Policy References */}
                {check.policyReferences && check.policyReferences.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Policy References
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {check.policyReferences.map((policy, index) => (
                        <span 
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                        >
                          <Shield className="h-3 w-3 mr-1" />
                          {policy}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Findings */}
                {check.findings && check.findings.length > 0 ? (
                  <div>
                    <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Findings ({check.findings.length})
                    </h5>
                    <div className="space-y-3">
                      {check.findings.map((finding) => (
                        <div 
                          key={finding.id}
                          className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden"
                        >
                          <div 
                            className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 cursor-pointer"
                            onClick={() => toggleFinding(finding.id)}
                          >
                            <div className="flex items-center space-x-3">
                              <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(finding.severity)}`}>
                                {finding.severity.charAt(0).toUpperCase() + finding.severity.slice(1)}
                              </span>
                              <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getFindingStatusColor(finding.status)}`}>
                                {finding.status.charAt(0).toUpperCase() + finding.status.slice(1)}
                              </span>
                              <p className="text-sm text-gray-900 dark:text-white">
                                {finding.description}
                              </p>
                            </div>
                            <div className="flex items-center">
                              {expandedFindings.includes(finding.id) ? (
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-500" />
                              )}
                            </div>
                          </div>
                          
                          {expandedFindings.includes(finding.id) && (
                            <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700">
                              <div className="space-y-3">
                                {finding.documentReference && (
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                      Document Reference
                                    </h6>
                                    <p className="text-sm text-gray-900 dark:text-white flex items-center">
                                      <FileText className="h-4 w-4 mr-1 text-blue-500" />
                                      {finding.documentReference}
                                    </p>
                                  </div>
                                )}
                                
                                {finding.policyReference && (
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                      Policy Reference
                                    </h6>
                                    <p className="text-sm text-gray-900 dark:text-white flex items-center">
                                      <Shield className="h-4 w-4 mr-1 text-blue-500" />
                                      {finding.policyReference}
                                    </p>
                                  </div>
                                )}
                                
                                {finding.resolutionSteps && (
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                      Resolution Steps
                                    </h6>
                                    <p className="text-sm text-gray-900 dark:text-white">
                                      {finding.resolutionSteps}
                                    </p>
                                  </div>
                                )}
                                
                                {finding.status === 'open' && (
                                  <div className="flex space-x-2 pt-2">
                                    <Button 
                                      size="sm" 
                                      onClick={() => markFindingAsResolved(finding.id)}
                                    >
                                      <CheckCircle className="h-4 w-4 mr-1" />
                                      Mark as Resolved
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      onClick={() => markFindingAsException(finding.id)}
                                    >
                                      <AlertCircle className="h-4 w-4 mr-1" />
                                      Mark as Exception
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : check.status === 'passed' ? (
                  <div className="flex items-center text-green-600 dark:text-green-400">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    <p>No compliance issues found.</p>
                  </div>
                ) : check.status === 'pending' || check.status === 'running' ? (
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <Clock className="h-5 w-5 mr-2" />
                    <p>QA check has not been run yet.</p>
                  </div>
                ) : null}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  )
}