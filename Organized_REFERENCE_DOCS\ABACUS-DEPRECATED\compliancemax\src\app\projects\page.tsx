
import React from 'react';

export default function Projects() {
  const projects = [
    {
      id: 1,
      name: 'GDPR Implementation',
      status: 'In Progress',
      progress: 65,
      dueDate: '2025-06-30',
      owner: '<PERSON>',
      priority: 'High'
    },
    {
      id: 2,
      name: 'ISO 27001 Certification',
      status: 'On Track',
      progress: 80,
      dueDate: '2025-08-15',
      owner: '<PERSON>',
      priority: 'Medium'
    },
    {
      id: 3,
      name: 'HIPAA Compliance',
      status: 'Attention Needed',
      progress: 40,
      dueDate: '2025-07-01',
      owner: '<PERSON>',
      priority: 'Critical'
    },
    {
      id: 4,
      name: 'SOC 2 Audit Preparation',
      status: 'Not Started',
      progress: 0,
      dueDate: '2025-09-30',
      owner: '<PERSON>',
      priority: 'Medium'
    }
  ];

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Projects</h1>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
          New Project
        </button>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <thead className="bg-gray-100 dark:bg-gray-700">
            <tr>
              <th className="py-3 px-4 text-left">Project Name</th>
              <th className="py-3 px-4 text-left">Status</th>
              <th className="py-3 px-4 text-left">Progress</th>
              <th className="py-3 px-4 text-left">Due Date</th>
              <th className="py-3 px-4 text-left">Owner</th>
              <th className="py-3 px-4 text-left">Priority</th>
              <th className="py-3 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
            {projects.map(project => (
              <tr key={project.id}>
                <td className="py-3 px-4">{project.name}</td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded text-xs ${
                    project.status === 'On Track' ? 'bg-green-100 text-green-800' :
                    project.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                    project.status === 'Attention Needed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {project.status}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full" 
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-xs">{project.progress}%</span>
                </td>
                <td className="py-3 px-4">{project.dueDate}</td>
                <td className="py-3 px-4">{project.owner}</td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded text-xs ${
                    project.priority === 'Critical' ? 'bg-red-100 text-red-800' :
                    project.priority === 'High' ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {project.priority}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <button className="text-blue-600 hover:text-blue-800 mr-2">Edit</button>
                  <button className="text-gray-600 hover:text-gray-800">View</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
