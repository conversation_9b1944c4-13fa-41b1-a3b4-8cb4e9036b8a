
import React from 'react';

export default function Documents() {
  const documents = [
    {
      id: 1,
      name: 'Privacy Policy v2.1',
      type: 'Policy',
      status: 'Approved',
      lastUpdated: '2025-05-10',
      owner: 'Legal Department',
      size: '245 KB'
    },
    {
      id: 2,
      name: 'Data Processing Agreement',
      type: 'Contract',
      status: 'Draft',
      lastUpdated: '2025-05-15',
      owner: 'Legal Department',
      size: '380 KB'
    },
    {
      id: 3,
      name: 'Security Assessment Report',
      type: 'Report',
      status: 'Under Review',
      lastUpdated: '2025-05-12',
      owner: 'IT Security',
      size: '1.2 MB'
    },
    {
      id: 4,
      name: 'GDPR Compliance Checklist',
      type: 'Checklist',
      status: 'Approved',
      lastUpdated: '2025-04-28',
      owner: 'Compliance Team',
      size: '125 KB'
    },
    {
      id: 5,
      name: 'Data Breach Response Plan',
      type: 'Procedure',
      status: 'Approved',
      lastUpdated: '2025-03-15',
      owner: 'IT Security',
      size: '450 KB'
    },
    {
      id: 6,
      name: 'Employee Data Protection Training',
      type: 'Training',
      status: 'Published',
      lastUpdated: '2025-04-05',
      owner: 'HR Department',
      size: '8.5 MB'
    }
  ];

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Document Management</h1>
        <div className="flex space-x-2">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
            Upload Document
          </button>
          <button className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded">
            Create Template
          </button>
        </div>
      </div>
      
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div className="flex items-center space-x-2">
          <input 
            type="text" 
            placeholder="Search documents..." 
            className="p-2 border border-gray-300 rounded w-full md:w-64"
          />
          <button className="bg-gray-200 hover:bg-gray-300 p-2 rounded">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          <label className="text-sm">Filter by:</label>
          <select className="p-2 border border-gray-300 rounded">
            <option value="">Document Type</option>
            <option value="policy">Policy</option>
            <option value="procedure">Procedure</option>
            <option value="contract">Contract</option>
            <option value="report">Report</option>
            <option value="checklist">Checklist</option>
          </select>
          <select className="p-2 border border-gray-300 rounded">
            <option value="">Status</option>
            <option value="approved">Approved</option>
            <option value="draft">Draft</option>
            <option value="review">Under Review</option>
            <option value="published">Published</option>
          </select>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <thead className="bg-gray-100 dark:bg-gray-700">
            <tr>
              <th className="py-3 px-4 text-left">Document Name</th>
              <th className="py-3 px-4 text-left">Type</th>
              <th className="py-3 px-4 text-left">Status</th>
              <th className="py-3 px-4 text-left">Last Updated</th>
              <th className="py-3 px-4 text-left">Owner</th>
              <th className="py-3 px-4 text-left">Size</th>
              <th className="py-3 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
            {documents.map(doc => (
              <tr key={doc.id}>
                <td className="py-3 px-4 flex items-center">
                  <span className="mr-2">📄</span>
                  {doc.name}
                </td>
                <td className="py-3 px-4">{doc.type}</td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded text-xs ${
                    doc.status === 'Approved' ? 'bg-green-100 text-green-800' :
                    doc.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
                    doc.status === 'Under Review' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {doc.status}
                  </span>
                </td>
                <td className="py-3 px-4">{doc.lastUpdated}</td>
                <td className="py-3 px-4">{doc.owner}</td>
                <td className="py-3 px-4">{doc.size}</td>
                <td className="py-3 px-4">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-800">View</button>
                    <button className="text-gray-600 hover:text-gray-800">Download</button>
                    <button className="text-gray-600 hover:text-gray-800">Share</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
