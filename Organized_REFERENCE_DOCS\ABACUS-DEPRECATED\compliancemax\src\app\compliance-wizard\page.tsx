
import React from 'react';

export default function ComplianceWizard() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Compliance Wizard</h1>
      
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div className="relative">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white">1</div>
            <div className="absolute top-0 right-0 -mr-1 -mt-1 w-4 h-4 rounded-full bg-green-500 border-2 border-white"></div>
          </div>
          <div className="h-1 flex-1 mx-2 bg-blue-600"></div>
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-700">2</div>
          <div className="h-1 flex-1 mx-2 bg-gray-300"></div>
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-700">3</div>
          <div className="h-1 flex-1 mx-2 bg-gray-300"></div>
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-700">4</div>
        </div>
        <div className="flex justify-between text-sm text-gray-600">
          <div className="text-blue-600 font-medium">Organization Profile</div>
          <div>Compliance Selection</div>
          <div>Requirements Assessment</div>
          <div>Action Plan</div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Organization Profile</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Please provide information about your organization to help us determine the applicable compliance requirements.
        </p>
        
        <form>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium mb-1">Organization Name</label>
              <input 
                type="text" 
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter organization name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Industry</label>
              <select className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Select industry</option>
                <option value="healthcare">Healthcare</option>
                <option value="finance">Finance & Banking</option>
                <option value="technology">Technology</option>
                <option value="retail">Retail</option>
                <option value="education">Education</option>
                <option value="government">Government</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Number of Employees</label>
              <select className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Select range</option>
                <option value="1-10">1-10</option>
                <option value="11-50">11-50</option>
                <option value="51-200">51-200</option>
                <option value="201-500">201-500</option>
                <option value="501-1000">501-1000</option>
                <option value="1000+">1000+</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Geographic Regions</label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input type="checkbox" id="region-na" className="mr-2" />
                  <label htmlFor="region-na">North America</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="region-eu" className="mr-2" />
                  <label htmlFor="region-eu">European Union</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="region-asia" className="mr-2" />
                  <label htmlFor="region-asia">Asia Pacific</label>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="region-other" className="mr-2" />
                  <label htmlFor="region-other">Other</label>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium mb-1">Types of Data Processed</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div className="flex items-center">
                <input type="checkbox" id="data-pii" className="mr-2" />
                <label htmlFor="data-pii">Personal Identifiable Information (PII)</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" id="data-payment" className="mr-2" />
                <label htmlFor="data-payment">Payment Card Information</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" id="data-health" className="mr-2" />
                <label htmlFor="data-health">Health Information</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" id="data-financial" className="mr-2" />
                <label htmlFor="data-financial">Financial Information</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" id="data-children" className="mr-2" />
                <label htmlFor="data-children">Children's Data</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" id="data-biometric" className="mr-2" />
                <label htmlFor="data-biometric">Biometric Data</label>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button type="button" className="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded mr-2">
              Save Draft
            </button>
            <button type="button" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
              Next: Compliance Selection
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
