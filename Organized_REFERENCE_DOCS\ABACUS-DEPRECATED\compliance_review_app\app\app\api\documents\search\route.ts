import { NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = "force-dynamic"

export async function GET(request: Request) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get("projectId")
    const query = searchParams.get("query") || ""
    const category = searchParams.get("category") || undefined
    
    if (!projectId) {
      return NextResponse.json(
        { message: "Project ID is required" },
        { status: 400 }
      )
    }

    // Check if user has access to this project
    const project = await prisma.project.findUnique({
      where: {
        id: projectId as string,
      },
    })

    if (!project) {
      return NextResponse.json(
        { message: "Project not found" },
        { status: 404 }
      )
    }

    if (project.userId !== user.id && user.role !== "ADMIN" && user.role !== "REVIEWER") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    // Build the query
    const whereClause: any = {
      projectId: projectId as string,
    }

    if (category && category !== "all") {
      whereClause.category = category
    }

    if (query) {
      whereClause.OR = [
        {
          name: {
            contains: query,
            mode: "insensitive",
          },
        },
        {
          category: {
            contains: query,
            mode: "insensitive",
          },
        },
      ]
    }

    // Get documents
    const documents = await prisma.document.findMany({
      where: whereClause,
      orderBy: {
        uploadedAt: "desc",
      },
      include: {
        step: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    })

    // Format the response
    const formattedDocuments = documents.map(doc => ({
      id: doc.id,
      name: doc.name,
      fileUrl: doc.fileUrl,
      fileType: doc.fileType,
      size: doc.size,
      uploadedAt: doc.uploadedAt,
      category: doc.category || undefined,
      stepId: doc.stepId,
      stepName: doc.step?.title,
    }))

    return NextResponse.json(formattedDocuments)
  } catch (error) {
    console.error("Error searching documents:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}