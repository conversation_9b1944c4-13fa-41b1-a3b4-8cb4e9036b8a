"use client"

import { motion } from "framer-motion"

interface StepCardProps {
  number: string
  title: string
  description: string
  index: number
}

export function StepCard({ number, title, description, index }: StepCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      viewport={{ once: true }}
      whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="p-6 bg-white dark:bg-gray-900 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 transition-all duration-300"
    >
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 text-xl font-bold mr-4 font-heading">
          {number}
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white font-heading">
          {title}
        </h3>
      </div>
      <p className="text-gray-600 dark:text-gray-400 ml-16">
        {description}
      </p>
    </motion.div>
  )
}