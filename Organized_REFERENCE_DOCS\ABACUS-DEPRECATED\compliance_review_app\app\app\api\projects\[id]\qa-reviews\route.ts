import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getCurrentUser } from "@/lib/auth"

export const dynamic = "force-dynamic"

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const project = await prisma.project.findUnique({
      where: {
        id: params.id,
      },
    })

    if (!project) {
      return NextResponse.json(
        { message: "Project not found" },
        { status: 404 }
      )
    }

    if (project.userId !== user.id && user.role !== "ADMIN" && user.role !== "REVIEWER") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    const qaReviews = await prisma.qAReview.findMany({
      where: {
        projectId: params.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(qaReviews)
  } catch (error) {
    console.error("Error fetching QA reviews:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (user.role !== "ADMIN" && user.role !== "REVIEWER") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    const project = await prisma.project.findUnique({
      where: {
        id: params.id,
      },
    })

    if (!project) {
      return NextResponse.json(
        { message: "Project not found" },
        { status: 404 }
      )
    }

    const { comments, status } = await request.json()

    const qaReview = await prisma.qAReview.create({
      data: {
        comments,
        status,
        projectId: params.id,
        reviewerId: user.id,
      },
    })

    // Update project status based on QA review
    await prisma.project.update({
      where: {
        id: params.id,
      },
      data: {
        status: status === "APPROVED" ? "COMPLETED" : 
               status === "REJECTED" ? "REJECTED" : 
               status === "NEEDS_REVISION" ? "IN_PROGRESS" : 
               "UNDER_REVIEW",
      },
    })

    return NextResponse.json(qaReview, { status: 201 })
  } catch (error) {
    console.error("Error creating QA review:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}