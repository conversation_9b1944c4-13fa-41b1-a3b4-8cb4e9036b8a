"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { Calendar, FileText, MoreHorizontal, Users } from "lucide-react"

import { formatDate, getStatusColor } from "@/lib/utils"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface ProjectCardProps {
  project: {
    id: string
    title: string
    description?: string | null
    status: string
    createdAt: Date
    updatedAt: Date
  }
  index: number
}

export function ProjectCard({ project, index }: ProjectCardProps) {
  const getProgressValue = (status: string) => {
    switch (status) {
      case "IN_PROGRESS":
        return 35
      case "UNDER_REVIEW":
        return 75
      case "COMPLETED":
        return 100
      case "REJECTED":
        return 100
      default:
        return 0
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ y: -5 }}
    >
      <Link href={`/projects/${project.id}`}>
        <Card className="h-full overflow-hidden hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-300 hover:shadow-medium">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-lg font-semibold line-clamp-1">
                {project.title}
              </CardTitle>
              <div className="flex items-center">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                    project.status
                  )}`}
                >
                  {project.status.replace("_", " ")}
                </span>
                <MoreHorizontal className="h-5 w-5 text-gray-400 ml-2" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">
              {project.description || "No description provided."}
            </p>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center text-sm mb-1">
                  <span className="text-gray-600 dark:text-gray-400">Progress</span>
                  <span className="font-medium">{getProgressValue(project.status)}%</span>
                </div>
                <Progress value={getProgressValue(project.status)} className="h-2" />
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <Calendar className="h-3.5 w-3.5 mr-1" />
                <span>{formatDate(project.updatedAt)}</span>
              </div>
              <div className="flex items-center">
                <FileText className="h-3.5 w-3.5 mr-1" />
                <span>Documents: 0</span>
              </div>
              <div className="flex items-center">
                <Users className="h-3.5 w-3.5 mr-1" />
                <span>Reviewers: 0</span>
              </div>
            </div>
          </CardFooter>
        </Card>
      </Link>
    </motion.div>
  )
}