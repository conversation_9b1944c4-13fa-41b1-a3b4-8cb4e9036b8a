"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Building2,
  Calculator,
  Check,
  CheckCircle,
  CheckSquare,
  ClipboardCheck,
  ClipboardList,
  Clock,
  FileCheck,
  FileDigit,
  FileScan,
  FileSpreadsheet,
  FileText,
  HardHat,
  HelpCircle,
  Info,
  Lightbulb,
  ListChecks,
  Scan,
  ScrollText,
  Shield,
  ShieldCheck,
  Upload,
  Users,
  Wand2,
  XCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { femaCategories, complianceQuestions } from "@/lib/data";

// Define the schema for each step
const step1Schema = z.object({
  applicantType: z.string().min(1, "Please select an applicant type"),
  disasterDeclaration: z.string().min(1, "Please select a disaster declaration"),
  categories: z.array(z.string()).min(1, "Please select at least one category"),
  estimatedCost: z.string().min(1, "Please select an estimated cost"),
});

const step2Schema = z.object({
  complianceArea: z.string().min(1, "Please select a compliance area"),
  completionStatus: z.string().min(1, "Please select a completion status"),
  documentationStatus: z.string().min(1, "Please select a documentation status"),
  notes: z.string().optional(),
});

// Define the 12 FEMA compliance areas
const femaComplianceAreas = [
  {
    id: "cbcs",
    name: "Consensus Based Codes and Standards (CBCS)",
    description: "Ensure permanent work complies with consensus-based codes and standards",
    icon: <ClipboardCheck className="h-5 w-5 text-primary" />,
    subOptions: [
      { id: "plans_review", name: "Plans/Specifications Review" },
      { id: "recommendations", name: "Actionable Recommendations" }
    ]
  },
  {
    id: "damage_inventory",
    name: "Damage Inventory/DDD/Scope/Cost",
    description: "Document detailed damage description, scope of work, and cost estimates",
    icon: <ClipboardList className="h-5 w-5 text-primary" />,
  },
  {
    id: "scope_cost",
    name: "Scope/Cost/Cost Reasonableness",
    description: "Ensure project scope and costs are reasonable and well-documented",
    icon: <FileSpreadsheet className="h-5 w-5 text-primary" />,
  },
  {
    id: "bca_mitigation",
    name: "Benefit Cost Analysis (BCA) for Mitigation",
    description: "Analyze costs and benefits for hazard mitigation measures",
    icon: <Calculator className="h-5 w-5 text-primary" />,
  },
  {
    id: "general_bca",
    name: "General BCA",
    description: "Conduct benefit-cost analysis for overall project justification",
    icon: <FileDigit className="h-5 w-5 text-primary" />,
  },
  {
    id: "ehp",
    name: "Environmental and Historic Preservation (EHP)",
    description: "Comply with environmental and historic preservation requirements",
    icon: <Building2 className="h-5 w-5 text-primary" />,
  },
  {
    id: "mitigation",
    name: "Mitigation",
    description: "Implement measures to reduce future disaster impacts",
    icon: <Shield className="h-5 w-5 text-primary" />,
  },
  {
    id: "insurance",
    name: "Insurance",
    description: "Meet insurance requirements for damaged facilities",
    icon: <ShieldCheck className="h-5 w-5 text-primary" />,
  },
  {
    id: "specific_policy",
    name: "Specific Policy Compliance",
    description: "Ensure compliance with specific FEMA policies relevant to your project",
    icon: <ScrollText className="h-5 w-5 text-primary" />,
  },
  {
    id: "general_policy",
    name: "Targeted General Policy Compliance",
    description: "Address general FEMA policy requirements applicable to your project",
    icon: <FileCheck className="h-5 w-5 text-primary" />,
  },
  {
    id: "document_organization",
    name: "Document Organization",
    description: "Organize project documentation with invoice/receipt scanning and integration",
    icon: <FileScan className="h-5 w-5 text-primary" />,
  },
  {
    id: "force_account",
    name: "Force Account Documentation",
    description: "Document force account labor, equipment, and materials with scanning integration",
    icon: <Scan className="h-5 w-5 text-primary" />,
  },
];

// Completion status options
const completionStatusOptions = [
  { id: "complete", name: "Complete", description: "All requirements have been met" },
  { id: "in_progress", name: "In Progress", description: "Requirements are being addressed" },
  { id: "not_started", name: "Not Started", description: "Work has not yet begun" },
  { id: "not_applicable", name: "Not Applicable", description: "Requirements do not apply to this project" },
];

// Documentation status options
const documentationStatusOptions = [
  { id: "documented", name: "Fully Documented", description: "All required documentation is in place" },
  { id: "partial", name: "Partially Documented", description: "Some documentation is available" },
  { id: "none", name: "No Documentation", description: "Documentation has not been prepared" },
];

export default function ComplianceWizardPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedComplianceArea, setSelectedComplianceArea] = useState("");
  const [complianceData, setComplianceData] = useState<Record<string, any>>({});
  const [selectedComplianceAreas, setSelectedComplianceAreas] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    applicantType: "",
    disasterDeclaration: "",
    categories: [] as string[],
    estimatedCost: "",
  });
  const [activeSubOptions, setActiveSubOptions] = useState<Record<string, string[]>>({});
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File[]>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [headerRef, headerInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [contentRef, contentInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  // Create form instances for each step
  const step1Form = useForm<z.infer<typeof step1Schema>>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      applicantType: formData.applicantType,
      disasterDeclaration: formData.disasterDeclaration,
      categories: formData.categories,
      estimatedCost: formData.estimatedCost,
    },
  });

  const step2Form = useForm<z.infer<typeof step2Schema>>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      complianceArea: selectedComplianceArea,
      completionStatus: complianceData[selectedComplianceArea]?.completionStatus || "",
      documentationStatus: complianceData[selectedComplianceArea]?.documentationStatus || "",
      notes: complianceData[selectedComplianceArea]?.notes || "",
    },
  });

  const totalSteps = 3; // Project Info, Compliance Areas, Summary
  const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;

  const handleStep1Submit = (data: z.infer<typeof step1Schema>) => {
    setFormData(data);
    setCurrentStep(2);
    window.scrollTo(0, 0);
  };

  const handleStep2Submit = (data: z.infer<typeof step2Schema>) => {
    // Save the compliance area data
    setComplianceData({
      ...complianceData,
      [data.complianceArea]: {
        completionStatus: data.completionStatus,
        documentationStatus: data.documentationStatus,
        notes: data.notes,
        subOptions: activeSubOptions[data.complianceArea] || [],
        files: uploadedFiles[data.complianceArea] || [],
      },
    });

    // Check if all selected compliance areas have been addressed
    const allSelectedAreasCompleted = selectedComplianceAreas.length > 0 && 
      selectedComplianceAreas.every(
        (areaId) => complianceData[areaId] || areaId === data.complianceArea
      );

    if (allSelectedAreasCompleted) {
      setCurrentStep(3); // Move to summary
    } else {
      // Find the next selected area that hasn't been completed
      const nextArea = selectedComplianceAreas.find(
        (areaId) => !complianceData[areaId] && areaId !== data.complianceArea
      );
      
      if (nextArea) {
        setSelectedComplianceArea(nextArea);
        step2Form.reset({
          complianceArea: nextArea,
          completionStatus: "",
          documentationStatus: "",
          notes: "",
        });
      } else {
        // If no next area, but we have some completed areas, go to summary
        setCurrentStep(3);
      }
    }
    
    window.scrollTo(0, 0);
  };

  const handleBack = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    } else if (currentStep === 3) {
      setCurrentStep(2);
    }
    window.scrollTo(0, 0);
  };

  const handleSubmit = () => {
    // In a real app, this would submit the data to the server
    console.log("Final form data:", { 
      ...formData, 
      selectedComplianceAreas,
      complianceAreas: complianceData 
    });
    
    toast({
      title: "Assessment Completed",
      description: "Your FEMA compliance assessment has been saved successfully.",
      duration: 3000,
    });
    
    router.push("/dashboard");
  };

  const handleSubOptionToggle = (areaId: string, optionId: string, e: React.MouseEvent) => {
    // Prevent the event from bubbling up to parent elements
    e.stopPropagation();
    
    setActiveSubOptions((prev) => {
      const currentOptions = prev[areaId] || [];
      if (currentOptions.includes(optionId)) {
        return {
          ...prev,
          [areaId]: currentOptions.filter((id) => id !== optionId),
        };
      } else {
        return {
          ...prev,
          [areaId]: [...currentOptions, optionId],
        };
      }
    });
  };

  const handleFileUpload = (areaId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const newFiles = Array.from(event.target.files);
      
      setUploadedFiles((prev) => ({
        ...prev,
        [areaId]: [...(prev[areaId] || []), ...newFiles],
      }));
      
      toast({
        title: "Files Uploaded",
        description: `${newFiles.length} file(s) uploaded successfully.`,
        duration: 3000,
      });
    }
  };

  const handleRemoveFile = (areaId: string, fileIndex: number, e: React.MouseEvent) => {
    // Prevent the event from bubbling up to parent elements
    e.stopPropagation();
    
    setUploadedFiles((prev) => {
      const updatedFiles = [...(prev[areaId] || [])];
      updatedFiles.splice(fileIndex, 1);
      return {
        ...prev,
        [areaId]: updatedFiles,
      };
    });
  };

  const triggerFileInput = (e: React.MouseEvent) => {
    // Prevent the event from bubbling up to parent elements
    e.stopPropagation();
    
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleComplianceAreaSelection = (areaId: string, e: React.MouseEvent) => {
    // Prevent the default behavior and stop propagation
    e.preventDefault();
    e.stopPropagation();
    
    if (selectedComplianceAreas.includes(areaId)) {
      setSelectedComplianceAreas(selectedComplianceAreas.filter(id => id !== areaId));
      // If we're removing the currently selected area, select another one
      if (selectedComplianceArea === areaId && selectedComplianceAreas.length > 1) {
        const nextArea = selectedComplianceAreas.find(id => id !== areaId);
        if (nextArea) {
          setSelectedComplianceArea(nextArea);
          step2Form.reset({
            complianceArea: nextArea,
            completionStatus: complianceData[nextArea]?.completionStatus || "",
            documentationStatus: complianceData[nextArea]?.documentationStatus || "",
            notes: complianceData[nextArea]?.notes || "",
          });
        }
      }
    } else {
      setSelectedComplianceAreas([...selectedComplianceAreas, areaId]);
      // If this is the first area being selected, make it the active one
      if (selectedComplianceAreas.length === 0) {
        setSelectedComplianceArea(areaId);
        step2Form.reset({
          complianceArea: areaId,
          completionStatus: "",
          documentationStatus: "",
          notes: "",
        });
      }
    }
  };

  // Handle click on compliance area in step 2
  const handleComplianceAreaClick = (areaId: string, e: React.MouseEvent) => {
    // Prevent the default behavior and stop propagation
    e.preventDefault();
    e.stopPropagation();
    
    setSelectedComplianceArea(areaId);
    step2Form.reset({
      complianceArea: areaId,
      completionStatus: complianceData[areaId]?.completionStatus || "",
      documentationStatus: complianceData[areaId]?.documentationStatus || "",
      notes: complianceData[areaId]?.notes || "",
    });
  };

  // Generate compliance status summary
  const getComplianceStatusSummary = () => {
    const totalAreas = selectedComplianceAreas.length;
    const completedAreas = Object.entries(complianceData)
      .filter(([areaId, data]) => 
        selectedComplianceAreas.includes(areaId) && 
        data.completionStatus === "complete"
      ).length;
    
    const inProgressAreas = Object.entries(complianceData)
      .filter(([areaId, data]) => 
        selectedComplianceAreas.includes(areaId) && 
        data.completionStatus === "in_progress"
      ).length;
    
    const notStartedAreas = Object.entries(complianceData)
      .filter(([areaId, data]) => 
        selectedComplianceAreas.includes(areaId) && 
        data.completionStatus === "not_started"
      ).length;
    
    const notApplicableAreas = Object.entries(complianceData)
      .filter(([areaId, data]) => 
        selectedComplianceAreas.includes(areaId) && 
        data.completionStatus === "not_applicable"
      ).length;

    return {
      totalAreas,
      completedAreas,
      inProgressAreas,
      notStartedAreas,
      notApplicableAreas,
      completionPercentage: totalAreas === 0 ? 0 : 
        Math.round((completedAreas / (totalAreas - notApplicableAreas)) * 100) || 0,
    };
  };

  // Generate recommendations based on compliance data
  const generateRecommendations = () => {
    const recommendations = [];

    // Add recommendations for incomplete areas
    selectedComplianceAreas.forEach((areaId) => {
      const area = femaComplianceAreas.find(a => a.id === areaId);
      const areaData = complianceData[areaId];
      
      if (!area) return;
      
      if (!areaData || areaData.completionStatus === "not_started") {
        recommendations.push({
          title: area.name,
          description: `Begin addressing ${area.name.toLowerCase()} requirements`,
          priority: "High",
          icon: <AlertTriangle className="h-5 w-5 text-destructive" />,
        });
      } else if (areaData.completionStatus === "in_progress" && areaData.documentationStatus !== "documented") {
        recommendations.push({
          title: area.name,
          description: `Complete documentation for ${area.name.toLowerCase()}`,
          priority: "Medium",
          icon: <FileText className="h-5 w-5 text-warning" />,
        });
      }
    });

    // Add specific recommendations based on compliance areas
    if (selectedComplianceAreas.includes("cbcs") && 
        complianceData["cbcs"] && 
        complianceData["cbcs"].completionStatus !== "complete") {
      recommendations.push({
        title: "CBCS Compliance",
        description: "Review building codes and standards applicable to your project",
        priority: "High",
        icon: <ClipboardCheck className="h-5 w-5 text-primary" />,
      });
    }

    if (selectedComplianceAreas.includes("ehp") && 
        complianceData["ehp"] && 
        complianceData["ehp"].completionStatus !== "complete") {
      recommendations.push({
        title: "EHP Review",
        description: "Complete Environmental and Historic Preservation review documentation",
        priority: "High",
        icon: <Building2 className="h-5 w-5 text-primary" />,
      });
    }

    if (selectedComplianceAreas.includes("document_organization") && 
        complianceData["document_organization"] && 
        complianceData["document_organization"].completionStatus !== "complete") {
      recommendations.push({
        title: "Document Organization",
        description: "Implement a systematic approach to organizing project documentation",
        priority: "Medium",
        icon: <FileScan className="h-5 w-5 text-primary" />,
      });
    }

    // Add general recommendations if needed
    if (recommendations.length < 3) {
      recommendations.push({
        title: "Regular Compliance Reviews",
        description: "Conduct regular reviews of all compliance areas to ensure ongoing adherence",
        priority: "Medium",
        icon: <ClipboardList className="h-5 w-5 text-primary" />,
      });
    }

    return recommendations;
  };

  const summary = getComplianceStatusSummary();
  const recommendations = generateRecommendations();

  // Check if we can proceed to step 2
  const canProceedToStep2 = selectedComplianceAreas.length > 0;
  
  // Check if we can proceed to step 3
  const canProceedToStep3 = Object.keys(complianceData).length > 0;

  return (
    <div className="space-y-8">
      <motion.div
        ref={headerRef}
        initial={{ opacity: 0, y: -20 }}
        animate={headerInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
        className="space-y-4"
      >
        <div className="flex flex-col">
          <h1 className="text-3xl font-bold tracking-tight">FEMA Compliance Wizard</h1>
          <p className="text-muted-foreground mt-2">
            Assess your compliance with FEMA Public Assistance requirements and get recommendations
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </motion.div>

      <motion.div
        ref={contentRef}
        initial={{ opacity: 0, y: 20 }}
        animate={contentInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
      >
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>
              {currentStep === 1 && "Project Information"}
              {currentStep === 2 && "Compliance Assessment"}
              {currentStep === 3 && "Compliance Summary"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {currentStep === 1 && (
              <div className="space-y-6">
                <Form {...step1Form}>
                  <form
                    onSubmit={step1Form.handleSubmit(handleStep1Submit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={step1Form.control}
                      name="applicantType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>What type of applicant are you?</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select applicant type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {complianceQuestions.general[0].options.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            This helps determine eligibility requirements for your organization.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={step1Form.control}
                      name="disasterDeclaration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>What is the disaster declaration number for your project?</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select disaster declaration" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {complianceQuestions.general[1].options.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            The disaster declaration number is required for all FEMA Public Assistance projects.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={step1Form.control}
                      name="categories"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Which FEMA Public Assistance categories apply to your project?</FormLabel>
                          <div className="space-y-2">
                            {complianceQuestions.general[2].options.map((option) => (
                              <div key={option} className="flex items-center gap-2">
                                <Checkbox
                                  id={option}
                                  checked={field.value.includes(option)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      field.onChange([...field.value, option]);
                                    } else {
                                      field.onChange(
                                        field.value.filter((value) => value !== option)
                                      );
                                    }
                                  }}
                                />
                                <Label htmlFor={option} className="text-sm">
                                  {option}
                                </Label>
                              </div>
                            ))}
                          </div>
                          <FormDescription>
                            Select all categories that apply to your project.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={step1Form.control}
                      name="estimatedCost"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>What is the estimated cost of your project?</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select estimated cost" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {complianceQuestions.general[3].options.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Project cost determines documentation requirements and review processes.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Separator />

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium mb-2">Select Compliance Areas to Assess</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Choose the specific compliance areas relevant to your project. You can select multiple areas.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {femaComplianceAreas.map((area) => (
                          <div
                            key={area.id}
                            className={`p-3 rounded-md border cursor-pointer transition-colors ${
                              selectedComplianceAreas.includes(area.id)
                                ? "bg-primary/10 border-primary"
                                : "bg-card border hover:bg-muted/50"
                            }`}
                            onClick={(e) => handleComplianceAreaSelection(area.id, e)}
                          >
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={selectedComplianceAreas.includes(area.id)}
                                onCheckedChange={(checked) => {
                                  const e = { preventDefault: () => {}, stopPropagation: () => {} } as React.MouseEvent;
                                  handleComplianceAreaSelection(area.id, e);
                                }}
                                className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                onClick={(e) => e.stopPropagation()}
                              />
                              {area.icon}
                              <span className="font-medium">{area.name}</span>
                            </div>
                            <p className="text-xs text-muted-foreground mt-1 ml-6">
                              {area.description}
                            </p>
                          </div>
                        ))}
                      </div>

                      {selectedComplianceAreas.length === 0 && (
                        <div className="bg-warning/10 p-4 rounded-md flex items-start gap-3 mt-4">
                          <AlertTriangle className="h-5 w-5 text-warning mt-0.5" />
                          <div>
                            <h3 className="font-medium">No Areas Selected</h3>
                            <p className="text-sm text-muted-foreground">
                              Please select at least one compliance area to proceed with the assessment.
                            </p>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end">
                      <Button 
                        type="submit" 
                        disabled={!canProceedToStep2 || selectedComplianceAreas.length === 0}
                      >
                        Next
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="bg-primary/10 p-4 rounded-md flex items-start gap-3">
                  <Info className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h3 className="font-medium">Compliance Assessment</h3>
                    <p className="text-sm text-muted-foreground">
                      Assess your compliance with each selected FEMA Public Assistance compliance area.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1 space-y-4">
                    <h3 className="text-lg font-medium">Selected Compliance Areas</h3>
                    <div className="space-y-2">
                      {selectedComplianceAreas.map((areaId) => {
                        const area = femaComplianceAreas.find(a => a.id === areaId);
                        if (!area) return null;
                        
                        const areaData = complianceData[area.id];
                        const isComplete = areaData?.completionStatus === "complete";
                        const isInProgress = areaData?.completionStatus === "in_progress";
                        const isNotApplicable = areaData?.completionStatus === "not_applicable";
                        
                        return (
                          <div
                            key={area.id}
                            className={`p-3 rounded-md cursor-pointer transition-colors ${
                              selectedComplianceArea === area.id
                                ? "bg-primary/10 border border-primary/30"
                                : isComplete
                                ? "bg-success/10 border border-success/30"
                                : isInProgress
                                ? "bg-warning/10 border border-warning/30"
                                : isNotApplicable
                                ? "bg-muted border border-muted/30"
                                : "bg-card border hover:bg-muted/50"
                            }`}
                            onClick={(e) => handleComplianceAreaClick(area.id, e)}
                          >
                            <div className="flex items-center gap-2">
                              {area.icon}
                              <span className="font-medium">{area.name}</span>
                              {isComplete && (
                                <CheckCircle className="h-4 w-4 text-success ml-auto" />
                              )}
                              {isInProgress && (
                                <Clock className="h-4 w-4 text-warning ml-auto" />
                              )}
                              {isNotApplicable && (
                                <XCircle className="h-4 w-4 text-muted-foreground ml-auto" />
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              {area.description}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    {selectedComplianceArea ? (
                      <Form {...step2Form}>
                        <form
                          onSubmit={step2Form.handleSubmit(handleStep2Submit)}
                          className="space-y-6"
                        >
                          <FormField
                            control={step2Form.control}
                            name="complianceArea"
                            render={({ field }) => (
                              <FormItem className="hidden">
                                <FormControl>
                                  <input type="hidden" {...field} />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <div className="space-y-2">
                            <h3 className="text-lg font-medium">
                              {femaComplianceAreas.find((area) => area.id === selectedComplianceArea)?.name}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {femaComplianceAreas.find((area) => area.id === selectedComplianceArea)?.description}
                            </p>
                          </div>

                          {/* Sub-options for CBCS */}
                          {selectedComplianceArea === "cbcs" && (
                            <div className="bg-muted/30 p-4 rounded-md space-y-3">
                              <h4 className="font-medium text-sm">Select applicable sub-options:</h4>
                              {femaComplianceAreas
                                .find((area) => area.id === "cbcs")
                                ?.subOptions?.map((option) => (
                                  <div key={option.id} className="flex items-center gap-2">
                                    <Checkbox
                                      id={option.id}
                                      checked={(activeSubOptions["cbcs"] || []).includes(option.id)}
                                      onCheckedChange={(checked) => {
                                        const e = { preventDefault: () => {}, stopPropagation: () => {} } as React.MouseEvent;
                                        handleSubOptionToggle("cbcs", option.id, e);
                                      }}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <Label htmlFor={option.id} className="text-sm">
                                      {option.name}
                                    </Label>
                                  </div>
                                ))}
                            </div>
                          )}

                          <FormField
                            control={step2Form.control}
                            name="completionStatus"
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel>Completion Status</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="space-y-1"
                                  >
                                    {completionStatusOptions.map((option) => (
                                      <div key={option.id} className="flex items-center space-x-2">
                                        <RadioGroupItem value={option.id} id={`status-${option.id}`} />
                                        <Label htmlFor={`status-${option.id}`} className="flex flex-col">
                                          <span>{option.name}</span>
                                          <span className="text-xs text-muted-foreground">
                                            {option.description}
                                          </span>
                                        </Label>
                                      </div>
                                    ))}
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={step2Form.control}
                            name="documentationStatus"
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel>Documentation Status</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="space-y-1"
                                  >
                                    {documentationStatusOptions.map((option) => (
                                      <div key={option.id} className="flex items-center space-x-2">
                                        <RadioGroupItem value={option.id} id={`doc-${option.id}`} />
                                        <Label htmlFor={`doc-${option.id}`} className="flex flex-col">
                                          <span>{option.name}</span>
                                          <span className="text-xs text-muted-foreground">
                                            {option.description}
                                          </span>
                                        </Label>
                                      </div>
                                    ))}
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Document upload section for document organization and force account */}
                          {(selectedComplianceArea === "document_organization" || selectedComplianceArea === "force_account") && (
                            <div className="space-y-3">
                              <FormLabel>Document Upload</FormLabel>
                              <div 
                                className="border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:bg-muted/10 transition-colors"
                                onClick={(e) => triggerFileInput(e)}
                              >
                                <input
                                  type="file"
                                  ref={fileInputRef}
                                  className="hidden"
                                  multiple
                                  onChange={(e) => handleFileUpload(selectedComplianceArea, e)}
                                  onClick={(e) => e.stopPropagation()}
                                />
                                <Upload className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                  Drag and drop files here, or click to select files
                                </p>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  type="button"
                                  onClick={(e) => triggerFileInput(e)}
                                >
                                  Select Files
                                </Button>
                                <p className="text-xs text-muted-foreground mt-2">
                                  Supported formats: PDF, JPG, PNG (Max size: 10MB)
                                </p>
                              </div>

                              {/* Display uploaded files */}
                              {uploadedFiles[selectedComplianceArea] && uploadedFiles[selectedComplianceArea].length > 0 && (
                                <div className="mt-4 space-y-2">
                                  <h4 className="text-sm font-medium">Uploaded Files</h4>
                                  <div className="space-y-2">
                                    {uploadedFiles[selectedComplianceArea].map((file, index) => (
                                      <div 
                                        key={index} 
                                        className="flex items-center justify-between bg-muted/30 p-2 rounded-md"
                                      >
                                        <div className="flex items-center gap-2">
                                          <FileText className="h-4 w-4 text-primary" />
                                          <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                                          <span className="text-xs text-muted-foreground">
                                            ({Math.round(file.size / 1024)} KB)
                                          </span>
                                        </div>
                                        <Button 
                                          variant="ghost" 
                                          size="sm" 
                                          type="button"
                                          onClick={(e) => handleRemoveFile(selectedComplianceArea, index, e)}
                                        >
                                          <XCircle className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                                        </Button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* BCA specific fields */}
                          {(selectedComplianceArea === "bca_mitigation" || selectedComplianceArea === "general_bca") && (
                            <div className="space-y-3">
                              <FormLabel>Benefit-Cost Ratio</FormLabel>
                              <div className="flex items-center gap-2">
                                <Input 
                                  type="number" 
                                  placeholder="Enter ratio (e.g., 1.5)" 
                                  className="max-w-[200px]" 
                                  step="0.01"
                                  min="0"
                                  onClick={(e) => e.stopPropagation()}
                                />
                                <p className="text-sm text-muted-foreground">
                                  Projects typically need a ratio ≥ 1.0 to be cost-effective
                                </p>
                              </div>
                            </div>
                          )}

                          <FormField
                            control={step2Form.control}
                            name="notes"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Notes (Optional)</FormLabel>
                                <FormControl>
                                  <Textarea
                                    className="min-h-[100px]"
                                    placeholder="Add any notes or comments about this compliance area"
                                    {...field}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex justify-between">
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={handleBack}
                            >
                              <ArrowLeft className="mr-2 h-4 w-4" />
                              Back
                            </Button>
                            <Button type="submit">
                              {Object.keys(complianceData).length === selectedComplianceAreas.length - 1 ? (
                                <>
                                  Review Summary
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </>
                              ) : (
                                <>
                                  Save and Continue
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </>
                              )}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    ) : (
                      <div className="flex items-center justify-center h-full p-8 bg-muted/20 rounded-md">
                        <div className="text-center">
                          <HelpCircle className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                          <h3 className="text-lg font-medium mb-2">No Compliance Area Selected</h3>
                          <p className="text-sm text-muted-foreground">
                            Please select a compliance area from the list on the left to begin assessment.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-8">
                <div className="bg-primary/10 p-4 rounded-md flex items-start gap-3">
                  <Info className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h3 className="font-medium">Assessment Complete</h3>
                    <p className="text-sm text-muted-foreground">
                      Based on your responses, we've generated the following FEMA Public Assistance compliance summary.
                    </p>
                  </div>
                </div>

                <div className="space-y-6">
                  <h3 className="text-lg font-medium">Compliance Status Overview</h3>
                  
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Overall Compliance</span>
                        <span className="font-medium">{summary.completionPercentage}%</span>
                      </div>
                      <Progress 
                        value={summary.completionPercentage} 
                        className="h-2.5"
                        indicatorClassName={
                          summary.completionPercentage >= 80 
                            ? "bg-success" 
                            : summary.completionPercentage >= 50 
                            ? "bg-warning" 
                            : "bg-destructive"
                        }
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      <div className="bg-success/10 p-4 rounded-md text-center">
                        <div className="text-2xl font-bold text-success">{summary.completedAreas}</div>
                        <div className="text-sm text-muted-foreground">Complete</div>
                      </div>
                      <div className="bg-warning/10 p-4 rounded-md text-center">
                        <div className="text-2xl font-bold text-warning">{summary.inProgressAreas}</div>
                        <div className="text-sm text-muted-foreground">In Progress</div>
                      </div>
                      <div className="bg-destructive/10 p-4 rounded-md text-center">
                        <div className="text-2xl font-bold text-destructive">{summary.notStartedAreas}</div>
                        <div className="text-sm text-muted-foreground">Not Started</div>
                      </div>
                      <div className="bg-muted p-4 rounded-md text-center">
                        <div className="text-2xl font-bold text-muted-foreground">{summary.notApplicableAreas}</div>
                        <div className="text-sm text-muted-foreground">Not Applicable</div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">Compliance Area Details</h3>
                    <div className="space-y-4">
                      {selectedComplianceAreas.map((areaId) => {
                        const area = femaComplianceAreas.find(a => a.id === areaId);
                        const areaData = complianceData[areaId];
                        
                        if (!area || !areaData) return null;
                        
                        const statusColor = 
                          areaData.completionStatus === "complete" 
                            ? "success" 
                            : areaData.completionStatus === "in_progress" 
                            ? "warning" 
                            : areaData.completionStatus === "not_applicable" 
                            ? "secondary" 
                            : "destructive";
                        
                        const docStatusColor = 
                          areaData.documentationStatus === "documented" 
                            ? "success" 
                            : areaData.documentationStatus === "partial" 
                            ? "warning" 
                            : "destructive";
                        
                        return (
                          <motion.div
                            key={area.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Card className="hover:shadow-md transition-shadow">
                              <CardContent className="p-4">
                                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                  <div className="flex items-center gap-3">
                                    {area.icon}
                                    <div>
                                      <h4 className="font-medium">{area.name}</h4>
                                      <p className="text-xs text-muted-foreground">{area.description}</p>
                                    </div>
                                  </div>
                                  <div className="flex flex-wrap gap-2">
                                    <Badge variant={statusColor as any}>
                                      {completionStatusOptions.find(s => s.id === areaData.completionStatus)?.name}
                                    </Badge>
                                    {areaData.completionStatus !== "not_applicable" && (
                                      <Badge variant={docStatusColor as any}>
                                        {documentationStatusOptions.find(s => s.id === areaData.documentationStatus)?.name}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                
                                {/* Show selected sub-options for CBCS */}
                                {area.id === "cbcs" && areaData.subOptions && areaData.subOptions.length > 0 && (
                                  <div className="mt-3 bg-muted/30 p-2 rounded-md">
                                    <span className="text-xs font-medium">Selected options:</span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {areaData.subOptions.map((optionId: string) => {
                                        const option = area.subOptions?.find(o => o.id === optionId);
                                        return option ? (
                                          <Badge key={optionId} variant="outline" className="text-xs">
                                            {option.name}
                                          </Badge>
                                        ) : null;
                                      })}
                                    </div>
                                  </div>
                                )}
                                
                                {/* Show uploaded files */}
                                {areaData.files && areaData.files.length > 0 && (
                                  <div className="mt-3 bg-muted/30 p-2 rounded-md">
                                    <span className="text-xs font-medium">Uploaded files:</span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {areaData.files.map((file: File, index: number) => (
                                        <Badge key={index} variant="outline" className="text-xs flex items-center gap-1">
                                          <FileText className="h-3 w-3" />
                                          {file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}
                                
                                {areaData.notes && (
                                  <div className="mt-3 text-sm bg-muted/50 p-2 rounded-md">
                                    <span className="font-medium">Notes:</span> {areaData.notes}
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-primary" />
                      Recommended Actions
                    </h3>
                    
                    <div className="space-y-4">
                      {recommendations.map((recommendation, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.1 * index }}
                        >
                          <Card className="hover:shadow-md transition-shadow">
                            <CardContent className="p-4">
                              <div className="flex items-start gap-4">
                                <div className="mt-1">{recommendation.icon}</div>
                                <div className="flex-1">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium">{recommendation.title}</h4>
                                    <Badge
                                      variant={
                                        recommendation.priority === "High"
                                          ? "destructive"
                                          : recommendation.priority === "Medium"
                                          ? "warning"
                                          : "secondary"
                                      }
                                    >
                                      {recommendation.priority} Priority
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {recommendation.description}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={handleBack}>
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back
                    </Button>
                    <Button onClick={handleSubmit}>
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      Complete Assessment
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}