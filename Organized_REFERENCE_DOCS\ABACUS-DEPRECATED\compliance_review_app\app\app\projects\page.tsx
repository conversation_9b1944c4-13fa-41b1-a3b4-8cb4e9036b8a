import Link from "next/link"

export default function ProjectsPage() {
  const projects = [
    {
      id: "1",
      title: "Hurricane Relief Project",
      description: "FEMA Public Assistance project for hurricane damage recovery in coastal areas.",
      status: "In Progress",
      progress: 35,
      createdAt: "June 15, 2023"
    },
    {
      id: "2", 
      title: "Flood Mitigation Plan",
      description: "Comprehensive flood mitigation plan for riverside communities affected by spring flooding.",
      status: "Under Review",
      progress: 75,
      createdAt: "May 10, 2023"
    },
    {
      id: "3",
      title: "Wildfire Recovery Initiative", 
      description: "Recovery project for public infrastructure damaged during the summer wildfires.",
      status: "Completed",
      progress: 100,
      createdAt: "March 5, 2023"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">F</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                FEMA Compliance
              </span>
            </Link>
            <nav className="flex space-x-1">
              <Link 
                href="/dashboard" 
                className="px-4 py-2 text-gray-700 hover:text-blue-600 text-sm font-medium"
              >
                Dashboard
              </Link>
              <Link 
                href="/projects" 
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium"
              >
                Projects
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Projects
            </h1>
            <p className="text-gray-600">
              Manage your FEMA compliance projects
            </p>
          </div>
          <Link 
            href="/projects/new"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium text-sm"
          >
            New Project
          </Link>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <div key={project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {project.title}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    project.status === "Completed" 
                      ? "bg-green-100 text-green-800"
                      : project.status === "Under Review"
                      ? "bg-yellow-100 text-yellow-800" 
                      : "bg-blue-100 text-blue-800"
                  }`}>
                    {project.status}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-4">
                  {project.description}
                </p>
                
                <div className="mb-4">
                  <div className="flex justify-between items-center text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>Created: {project.createdAt}</span>
                  <Link 
                    href={`/projects/${project.id}`}
                    className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs font-medium"
                  >
                    View
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  )
}