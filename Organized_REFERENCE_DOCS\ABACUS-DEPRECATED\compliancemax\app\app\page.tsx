"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, CheckCircle, FileText, Shield, Users } from "lucide-react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";

export default function Home() {
  const [heroRef, heroInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [featuresRef, featuresInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [statsRef, statsInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div className="flex flex-col items-center">
      <motion.section
        ref={heroRef}
        initial={{ opacity: 0, y: 20 }}
        animate={heroInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6 }}
        className="relative w-full max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:px-8 flex flex-col items-center text-center"
      >
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-blue-50 to-white dark:from-blue-950 dark:to-background opacity-70"></div>
        </div>
        
        <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
          Simplify Your{" "}
          <span className="text-primary">FEMA Public Assistance Compliance</span>
        </h1>
        <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mb-10">
          ComplianceMax helps organizations streamline FEMA Public Assistance compliance processes, reduce risks, and ensure regulatory adherence with our comprehensive management solution.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href="/dashboard">
            <Button size="lg" className="gap-2">
              Go to Dashboard <ArrowRight size={16} />
            </Button>
          </Link>
          <Link href="/projects">
            <Button size="lg" variant="outline" className="gap-2">
              View Projects <ArrowRight size={16} />
            </Button>
          </Link>
        </div>
      </motion.section>

      <motion.section
        ref={featuresRef}
        initial={{ opacity: 0, y: 20 }}
        animate={featuresInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="w-full max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 bg-secondary/50 rounded-lg"
      >
        <h2 className="text-3xl font-bold text-center mb-12">Key Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            {
              icon: <Shield className="h-10 w-10 text-primary" />,
              title: "FEMA Compliance Dashboard",
              description:
                "Get real-time insights into your FEMA Public Assistance compliance status with intuitive visualizations and metrics.",
            },
            {
              icon: <FileText className="h-10 w-10 text-primary" />,
              title: "Document Management",
              description:
                "Centralize and organize all FEMA-related documents with powerful search and categorization.",
            },
            {
              icon: <CheckCircle className="h-10 w-10 text-primary" />,
              title: "Compliance Wizard",
              description:
                "Step-by-step guidance through FEMA Public Assistance compliance assessments with actionable recommendations.",
            },
            {
              icon: <Users className="h-10 w-10 text-primary" />,
              title: "Project Management",
              description:
                "Track FEMA Public Assistance projects, assign tasks, and monitor progress in one unified platform.",
            },
          ].map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={featuresInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className="bg-card p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
            >
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      <motion.section
        ref={statsRef}
        initial={{ opacity: 0, y: 20 }}
        animate={statsInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="w-full max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            { value: "85%", label: "Reduction in compliance issues" },
            { value: "60%", label: "Time saved on documentation" },
            { value: "95%", label: "Successful FEMA reimbursements" },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={statsInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className="bg-primary/10 dark:bg-primary/20 p-8 rounded-lg text-center"
            >
              <div className="text-4xl font-bold text-primary mb-2">
                {stat.value}
              </div>
              <div className="text-muted-foreground">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </motion.section>
    </div>
  );
}