import { redirect } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { getCurrentUser } from "@/lib/auth"
import { PolicyIngestion } from "@/components/qa-engine/policy-ingestion"
import { AutomatedQA } from "@/components/qa-engine/automated-qa"
import { OpenAIIntegration } from "@/components/qa-engine/openai-integration"
import { ExceptionManagement } from "@/components/qa-engine/exception-management"

export const dynamic = "force-dynamic"

export default async function QAEnginePage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/login")
  }

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          QA Engine
        </h1>
        <p className="mt-1 text-gray-600 dark:text-gray-400">
          Automated compliance validation and quality assurance tools
        </p>
      </div>

      <Tabs defaultValue="automated-qa" className="space-y-6">
        <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full">
          <TabsTrigger value="automated-qa">Automated QA</TabsTrigger>
          <TabsTrigger value="policy-ingestion">Policy Repository</TabsTrigger>
          <TabsTrigger value="openai-integration">AI Validation</TabsTrigger>
          <TabsTrigger value="exception-management">Exceptions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="automated-qa" className="space-y-6">
          <AutomatedQA projectId="project-1" />
        </TabsContent>
        
        <TabsContent value="policy-ingestion" className="space-y-6">
          <PolicyIngestion />
        </TabsContent>
        
        <TabsContent value="openai-integration" className="space-y-6">
          <OpenAIIntegration projectId="project-1" />
        </TabsContent>
        
        <TabsContent value="exception-management" className="space-y-6">
          <ExceptionManagement projectId="project-1" />
        </TabsContent>
      </Tabs>
    </div>
  )
}