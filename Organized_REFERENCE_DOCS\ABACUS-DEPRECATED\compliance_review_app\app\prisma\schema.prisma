// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/compliance_review_app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model User {
  id             String    @id @default(uuid())
  email          String    @unique
  name           String?
  password       String
  role           Role      @default(USER)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  projects       Project[]
  documents      Document[]
  qaReviews      QAReview[]
}

enum Role {
  USER
  ADMIN
  REVIEWER
}

model Project {
  id             String    @id @default(uuid())
  title          String
  description    String?
  status         ProjectStatus @default(IN_PROGRESS)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  userId         String
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  documents      Document[]
  complianceSteps ComplianceStep[]
  qaReviews      QAReview[]
}

enum ProjectStatus {
  IN_PROGRESS
  UNDER_REVIEW
  COMPLETED
  REJECTED
}

model Document {
  id             String    @id @default(uuid())
  name           String
  fileUrl        String
  fileType       String
  size           Int
  uploadedAt     DateTime  @default(now())
  projectId      String
  project        Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  userId         String
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  stepId         String?
  step           ComplianceStep? @relation(fields: [stepId], references: [id])
  category       String?
}

model ComplianceStep {
  id             String    @id @default(uuid())
  title          String
  description    String?
  order          Int
  isCompleted    Boolean   @default(false)
  projectId      String
  project        Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  documents      Document[]
  questions      Question[]
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model Question {
  id             String    @id @default(uuid())
  text           String
  answer         String?
  isRequired     Boolean   @default(true)
  stepId         String
  step           ComplianceStep @relation(fields: [stepId], references: [id], onDelete: Cascade)
}

model QAReview {
  id             String    @id @default(uuid())
  comments       String?
  status         QAStatus  @default(PENDING)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  projectId      String
  project        Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  reviewerId     String
  reviewer       User      @relation(fields: [reviewerId], references: [id])
}

enum QAStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_REVISION
}