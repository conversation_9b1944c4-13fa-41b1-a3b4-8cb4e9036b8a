{"name": "@apidevtools/openapi-schemas", "version": "2.1.0", "description": "JSON Schemas for every version of the OpenAPI Specification", "keywords": ["openapi", "open-api", "swagger", "oas", "api", "rest", "json", "specification", "definition", "schema"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "license": "MIT", "homepage": "https://apitools.dev/openapi-schemas", "repository": {"type": "git", "url": "https://github.com/APIDevTools/openapi-schemas.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib", "schemas"], "scripts": {"clean": "shx rm -rf .nyc_output coverage lib .tmp schemas", "clone": "git clone https://github.com/OAI/OpenAPI-Specification.git .tmp", "copy": "shx cp -r .tmp/schemas schemas", "lint": "eslint src test", "build": "npm run build:schemas && npm run build:typescript", "build:schemas": "npm run clean && npm run clone && npm run copy", "build:typescript": "tsc", "watch": "tsc --watch", "test": "mocha && npm run lint", "coverage": "nyc node_modules/mocha/bin/mocha", "upgrade": "npm-check -u && npm audit fix", "bump": "bump --tag --push --all", "release": "npm run upgrade && npm run clean && npm run build && npm test && npm run bump"}, "engines": {"node": ">=10"}, "devDependencies": {"@jsdevtools/eslint-config": "^1.1.4", "@jsdevtools/version-bump-prompt": "^6.1.0", "@types/chai": "^4.2.17", "@types/command-line-args": "^5.0.0", "@types/mocha": "^8.2.2", "@types/node": "^15.0.1", "chai": "^4.3.4", "eslint": "^7.25.0", "mocha": "^8.3.2", "npm-check": "^5.9.2", "nyc": "^15.1.0", "shx": "^0.3.3", "typescript": "^4.2.4"}}