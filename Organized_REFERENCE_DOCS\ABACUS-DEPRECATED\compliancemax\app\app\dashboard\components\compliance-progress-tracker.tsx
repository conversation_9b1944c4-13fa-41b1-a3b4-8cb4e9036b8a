"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Al<PERSON><PERSON>riangle, CheckCircle, ChevronDown, ChevronUp, Clock, Info } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

// TypeScript interfaces for the component
export interface ComplianceCategory {
  id: string;
  name: string;
  progress: number;
  status: "complete" | "in-progress" | "attention";
  items?: ComplianceItem[];
}

export interface ComplianceItem {
  id: string;
  name: string;
  completed: boolean;
}

export interface ComplianceProgressTrackerProps {
  title?: string;
  description?: string;
  categories: ComplianceCategory[];
  className?: string;
}

export const ComplianceProgressTracker = ({
  title = "FEMA Compliance Progress",
  description = "Track your FEMA Public Assistance compliance progress across categories",
  categories,
  className,
}: ComplianceProgressTrackerProps) => {
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [ref, inView] = useInView({ triggerOnce: true, threshold: 0.1 });
  
  // Calculate overall progress
  const overallProgress = categories.length > 0
    ? Math.round(
        categories.reduce((sum, category) => sum + category.progress, 0) / categories.length
      )
    : 0;

  // Toggle category expansion
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  // Get status icon based on status
  const getStatusIcon = (status: ComplianceCategory["status"]) => {
    switch (status) {
      case "complete":
        return <CheckCircle className="h-5 w-5 text-success" />;
      case "in-progress":
        return <Clock className="h-5 w-5 text-warning" />;
      case "attention":
        return <AlertTriangle className="h-5 w-5 text-destructive" />;
      default:
        return <Info className="h-5 w-5 text-muted-foreground" />;
    }
  };

  // Get status color based on status
  const getStatusColor = (status: ComplianceCategory["status"]) => {
    switch (status) {
      case "complete":
        return "success";
      case "in-progress":
        return "warning";
      case "attention":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Get progress color based on progress percentage
  const getProgressColor = (progress: number) => {
    if (progress >= 100) return "bg-success";
    if (progress >= 60) return "bg-warning";
    return "bg-destructive";
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5 }}
      className={cn("space-y-4", className)}
    >
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle>{title}</CardTitle>
          <p className="text-sm text-muted-foreground">{description}</p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium">Overall Compliance</span>
              <span className="font-medium">{overallProgress}%</span>
            </div>
            <Progress 
              value={overallProgress} 
              className="h-2.5"
              indicatorClassName={getProgressColor(overallProgress)}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{categories.filter(c => c.status === "complete").length} of {categories.length} categories complete</span>
              <span>
                {categories.filter(c => c.status === "attention").length > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {categories.filter(c => c.status === "attention").length} need attention
                  </Badge>
                )}
              </span>
            </div>
          </div>

          <Separator />

          {/* Categories Breakdown */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Categories Breakdown</h3>
            <div className="space-y-3">
              {categories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.3, delay: 0.05 * index }}
                >
                  <div className="space-y-2">
                    <div 
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => toggleCategory(category.id)}
                    >
                      <div className="flex items-center gap-2">
                        {getStatusIcon(category.status)}
                        <span className="font-medium">{category.name}</span>
                        <Badge variant={getStatusColor(category.status) as any}>
                          {category.progress}%
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        {category.items && category.items.length > 0 && (
                          <span className="text-xs text-muted-foreground">
                            {category.items.filter(item => item.completed).length} of {category.items.length} complete
                          </span>
                        )}
                        {expandedCategories[category.id] ? (
                          <ChevronUp className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                    </div>
                    <Progress 
                      value={category.progress} 
                      className="h-2"
                      indicatorClassName={getProgressColor(category.progress)}
                    />
                    
                    {/* Expanded Items */}
                    {expandedCategories[category.id] && category.items && category.items.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="pl-7 pt-2 space-y-2"
                      >
                        {category.items.map((item) => (
                          <div key={item.id} className="flex items-center gap-2 text-sm">
                            {item.completed ? (
                              <CheckCircle className="h-4 w-4 text-success" />
                            ) : (
                              <Clock className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className={item.completed ? "" : "text-muted-foreground"}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ComplianceProgressTracker;