"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { 
  <PERSON><PERSON>ircle<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  FileText, 
  Shield, 
  Upload 
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { FeatureCard } from "@/components/home/<USER>"
import { StepCard } from "@/components/home/<USER>"

export function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden hero-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
          >
            <motion.div 
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center md:text-left"
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white leading-tight font-heading">
                Streamline Your FEMA <span className="text-blue-600 dark:text-blue-400">Compliance</span> Process
              </h1>
              <p className="mt-6 text-lg text-gray-600 dark:text-gray-300">
                Our multi-step wizard guides you through FEMA Public Assistance compliance, 
                with document management, automated QA, and comprehensive reporting.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                <Button asChild size="lg" className="text-base px-8 py-6 gradient-button">
                  <Link href="/register">
                    Get Started
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="text-base px-8 py-6">
                  <Link href="/login">
                    Sign In
                  </Link>
                </Button>
              </div>
            </motion.div>
            <motion.div 
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative h-[300px] md:h-[400px] lg:h-[500px]"
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative h-full w-full max-w-md mx-auto">
                  <Image
                    src="/image.png"
                    alt="FEMA Compliance Dashboard"
                    fill
                    className="object-contain rounded-lg shadow-xl"
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-heading">
              Key Features
            </h2>
            <p className="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Everything you need to streamline your FEMA compliance process
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group">
              <FeatureCard
                icon={<ClipboardCheck className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Multi-Step Wizard"
                description="Guided process that walks you through each step of FEMA compliance requirements."
                index={0}
              />
            </div>
            <div className="group">
              <FeatureCard
                icon={<Upload className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Document Management"
                description="Easily upload, organize, and manage all required documentation in one place."
                index={1}
              />
            </div>
            <div className="group">
              <FeatureCard
                icon={<Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Automated QA"
                description="Built-in quality assurance checks to ensure compliance with FEMA requirements."
                index={2}
              />
            </div>
            <div className="group">
              <FeatureCard
                icon={<FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Comprehensive Reports"
                description="Generate detailed reports for submission and record-keeping purposes."
                index={3}
              />
            </div>
            <div className="group">
              <FeatureCard
                icon={<CheckCircle2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Compliance Tracking"
                description="Monitor your progress and track compliance status across all projects."
                index={4}
              />
            </div>
            <div className="group">
              <FeatureCard
                icon={<Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                title="Secure & Reliable"
                description="Enterprise-grade security to protect your sensitive compliance data."
                index={5}
              />
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-heading">
              How It Works
            </h2>
            <p className="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our streamlined process makes FEMA compliance simple and efficient
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <StepCard
              number="01"
              title="Create Project"
              description="Start by creating a new compliance project and entering basic information."
              index={0}
            />
            <StepCard
              number="02"
              title="Follow Wizard"
              description="Complete each step of the compliance wizard, uploading required documents."
              index={1}
            />
            <StepCard
              number="03"
              title="Generate Report"
              description="Once complete, generate a comprehensive compliance report for submission."
              index={2}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-fema-blue dark:bg-blue-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 font-heading">
              Ready to Streamline Your FEMA Compliance Process?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-10">
              Join thousands of organizations that trust our platform for their FEMA Public Assistance compliance needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="text-base px-8 py-6">
                <Link href="/register">
                  Get Started for Free
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-base px-8 py-6 bg-transparent text-white border-white hover:bg-white/10">
                <Link href="/contact">
                  Contact Sales
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}