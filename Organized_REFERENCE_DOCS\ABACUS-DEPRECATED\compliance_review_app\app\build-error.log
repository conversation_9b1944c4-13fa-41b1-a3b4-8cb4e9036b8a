
> app@0.1.0 build
> next build

   ▲ Next.js 15.3.2
   - Environments: .env

   Creating an optimized production build ...
Failed to compile.

app/layout.tsx
An error occurred in `next/font`.

Error: Cannot find module '/home/<USER>/compliance_review_app/node_modules/enhanced-resolve/lib/index.js'. Please verify that the package.json has a valid "main" entry
    at tryPackage (node:internal/modules/cjs/loader:493:19)
    at Function._findPath (node:internal/modules/cjs/loader:775:18)
    at Function.<anonymous> (node:internal/modules/cjs/loader:1211:27)
    at /home/<USER>/compliance_review_app/app/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1311:12)
    at mod.require (/home/<USER>/compliance_review_app/app/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)


> Build failed because of webpack errors
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path /home/<USER>/compliance_review_app/app
npm error workspace app@0.1.0
npm error location /home/<USER>/compliance_review_app/app
npm error command failed
npm error command sh -c next build
