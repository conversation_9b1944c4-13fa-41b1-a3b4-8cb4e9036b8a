import { NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = "force-dynamic"

// This is a simplified version for the demo
// In a real app, you would use a library like formidable or next-connect
// to handle file uploads, and store files in a service like S3
export async function POST(request: Request) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const projectId = formData.get("projectId") as string
    const stepId = formData.get("stepId") as string | null
    const category = formData.get("category") as string | null
    const file = formData.get("file") as File

    if (!projectId) {
      return NextResponse.json(
        { message: "Project ID is required" },
        { status: 400 }
      )
    }

    if (!file) {
      return NextResponse.json(
        { message: "File is required" },
        { status: 400 }
      )
    }

    // Check if user has access to this project
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
      },
    })

    if (!project) {
      return NextResponse.json(
        { message: "Project not found" },
        { status: 404 }
      )
    }

    if (project.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      )
    }

    // In a real app, you would upload the file to a storage service
    // and get a URL back. For this demo, we'll create a fake URL.
    const fileUrl = `/api/documents/fake-url-${Date.now()}`

    // Create document record in the database
    const document = await prisma.document.create({
      data: {
        name: file.name,
        fileUrl,
        fileType: file.type,
        size: file.size,
        projectId,
        userId: user.id,
        stepId: stepId || undefined,
        category: category || undefined,
      },
    })

    return NextResponse.json(document, { status: 201 })
  } catch (error) {
    console.error("Error uploading document:", error)
    return NextResponse.json(
      { message: "Something went wrong" },
      { status: 500 }
    )
  }
}