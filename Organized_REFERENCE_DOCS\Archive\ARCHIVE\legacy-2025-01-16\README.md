# Legacy Code Archive - 2025-01-16

## Archived During Task 1-4: Delete/Archive duplicate Express & HTML servers

This directory contains legacy code that was superseded by the unified Next.js application in the `/app` directory.

## Archived Components

### 1. `api/` - Duplicate FastAPI Server
- **Original Location**: `/api/main.py` (311 lines)
- **Reason for Archival**: Duplicate FastAPI implementation replaced by Next.js API routes
- **Functionality Preserved In**: `/app/app/api/` directory with Next.js API routes

### 2. `backend/` - FastAPI Backend
- **Original Location**: `/backend/main.py` (459 lines) 
- **Reason for Archival**: FastAPI server functionality moved to Next.js application
- **Functionality Preserved In**: `/app/app/api/` directory and integrated services

### 3. `frontend/` - Legacy HTML Frontend
- **Original Location**: `/SRC/frontend/` 
- **Contents**: 5 HTML files (2,422 total lines)
  - `index.html` (516 lines)
  - `index-technical.html` (358 lines)
  - `index-enterprise.html` (161 lines)
  - `intelligent-onboarding.html` (882 lines)
  - `onboarding.html` (505 lines)
- **Reason for Archival**: Static HTML replaced by React components and Next.js app
- **Functionality Preserved In**: `/app/app/` with modern React components

### 4. `wizard/` - Standalone React Components
- **Original Location**: `/wizard/`
- **Contents**: React wizard components
  - `questionnaire.tsx` (130 lines)
  - `wizard-layout.tsx` (192 lines)
  - `document-upload.tsx` (157 lines)
- **Reason for Archival**: Superseded by integrated Next.js app components
- **Functionality Preserved In**: `/app/app/compliance-wizard/` and `/app/components/`

## What Was Preserved

### Critical Components Kept Active:
- **SRC/database/schema.sql** - PostgreSQL schema (unique functionality)
- **SRC/migration.js** - Data migration script (critical for data import)
- **SRC/services/enhanced_policy_matcher.py** - AI/ML engine (unique business logic)
- **DOCS/** - Business rules and compliance data (source of truth)

## Archive Date
**January 16, 2025** - Task 1-4 completion

## Restoration Instructions
If any functionality needs to be restored from this archive:
1. Copy the specific component from this archive
2. Integrate with the current Next.js application in `/app`
3. Update import paths and dependencies as needed
4. Test integration with the unified codebase

## Verification
All archived functionality has been verified to exist and work properly in the unified Next.js application at `/app`. 