import { compare, hash } from 'bcrypt';
import { jwtVerify, SignJWT } from 'jose';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { prisma } from './prisma';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'default_secret_please_change_in_production'
);

export async function hashPassword(password: string) {
  return hash(password, 10);
}

export async function comparePasswords(
  plainTextPassword: string,
  hashedPassword: string
) {
  return compare(plainTextPassword, hashedPassword);
}

export async function createJWT(user: { id: string; email: string; role: string }) {
  const token = await new SignJWT({
    id: user.id,
    email: user.email,
    role: user.role,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .sign(JWT_SECRET);

  return token;
}

export async function verifyJWT(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

export async function getSession() {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  if (!token) {
    return null;
  }

  const payload = await verifyJWT(token);
  return payload;
}

export async function getCurrentUser() {
  const session = await getSession();

  if (!session?.id) {
    return null;
  }

  const user = await prisma.user.findUnique({
    where: {
      id: session.id as string,
    },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
    },
  });

  return user;
}

export async function validateRequest(request: NextRequest) {
  const token = request.cookies.get('token')?.value;

  if (!token) {
    return { user: null };
  }

  const payload = await verifyJWT(token);

  if (!payload) {
    return { user: null };
  }

  const user = await prisma.user.findUnique({
    where: {
      id: payload.id as string,
    },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
    },
  });

  return { user };
}