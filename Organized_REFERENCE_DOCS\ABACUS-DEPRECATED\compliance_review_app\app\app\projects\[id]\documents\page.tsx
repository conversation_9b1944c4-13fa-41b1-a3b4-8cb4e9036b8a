import Link from "next/link"

export default function DocumentsPage({ params }: { params: { id: string } }) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-xl font-bold text-gray-900">
                FEMA Compliance
              </span>
            </Link>
            <nav className="flex space-x-4">
              <Link 
                href="/dashboard" 
                className="px-4 py-2 text-gray-700 hover:text-blue-600"
              >
                Dashboard
              </Link>
              <Link 
                href="/projects" 
                className="px-4 py-2 text-gray-700 hover:text-blue-600"
              >
                Projects
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Document Management
            </h1>
            <p className="text-gray-600">
              Upload and manage documents for Hurricane Relief Project
            </p>
          </div>
          <Link 
            href={`/projects/${params.id}`}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Back to Project
          </Link>
        </div>

        {/* Upload Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Upload Documents
          </h2>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <p className="text-lg font-medium text-gray-900 mb-2">
              Drag and drop files here or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports PDF, Word, Excel, and image files (max 10MB each)
            </p>
            <button className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Choose Files
            </button>
          </div>
        </div>

        {/* Documents List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Uploaded Documents
            </h2>
          </div>
          <div className="divide-y divide-gray-200">
            <div className="p-6 flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-3 bg-red-100 rounded-md mr-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Damage Assessment Report.pdf
                  </h3>
                  <div className="mt-1 flex items-center text-sm text-gray-500">
                    <span>2.5 MB</span>
                    <span className="mx-2">•</span>
                    <span>Uploaded June 16, 2023</span>
                    <span className="mx-2">•</span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                      Damage Inventory
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Preview
                </button>
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Download
                </button>
              </div>
            </div>

            <div className="p-6 flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-md mr-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Cost Estimate Worksheet.xlsx
                  </h3>
                  <div className="mt-1 flex items-center text-sm text-gray-500">
                    <span>1.8 MB</span>
                    <span className="mx-2">•</span>
                    <span>Uploaded June 18, 2023</span>
                    <span className="mx-2">•</span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                      Cost Documentation
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Preview
                </button>
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Download
                </button>
              </div>
            </div>

            <div className="p-6 flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 rounded-md mr-4">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Project Photos.zip
                  </h3>
                  <div className="mt-1 flex items-center text-sm text-gray-500">
                    <span>15.0 MB</span>
                    <span className="mx-2">•</span>
                    <span>Uploaded June 20, 2023</span>
                    <span className="mx-2">•</span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                      Damage Inventory
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Preview
                </button>
                <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50">
                  Download
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}