"use client"

import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { 
  Upload, 
  FileText, 
  CheckCircle2, 
  Clock, 
  UserPlus 
} from "lucide-react"

import { formatDate } from "@/lib/utils"
import { Activity, activityIconMap } from "@/lib/dummy-data"

interface RecentActivityProps {
  activities: Activity[]
}

export function RecentActivity({ activities }: RecentActivityProps) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "document_upload":
        return <Upload className="h-5 w-5 text-blue-500" />;
      case "project_created":
        return <FileText className="h-5 w-5 text-green-500" />;
      case "step_completed":
        return <CheckCircle2 className="h-5 w-5 text-purple-500" />;
      case "review_submitted":
        return <Clock className="h-5 w-5 text-orange-500" />;
      case "user_added":
        return <UserPlus className="h-5 w-5 text-indigo-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  }

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden"
    >
      <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-heading">Recent Activity</h3>
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {activities.length > 0 ? (
          activities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="px-6 py-4 flex items-start"
            >
              <div className="flex-shrink-0 mr-4">
                <div className="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  {getActivityIcon(activity.type)}
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm text-gray-900 dark:text-white font-medium">
                  {activity.description}
                </p>
                {activity.projectTitle && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Project: {activity.projectTitle}
                  </p>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {formatDate(activity.timestamp)}
                </p>
              </div>
            </motion.div>
          ))
        ) : (
          <div className="px-6 py-8 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        )}
      </div>
    </motion.div>
  )
}