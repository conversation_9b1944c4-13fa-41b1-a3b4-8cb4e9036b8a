
> app@0.1.0 build
> next build

   ▲ Next.js 15.3.2
   - Environments: .env

   Creating an optimized production build ...
[Error: Cannot apply unknown utility class: border-border]
 ✓ Compiled successfully in 14.0s
   Skipping validation of types
   Skipping linting
   Collecting page data ...
   Generating static pages (0/4) ...
   Generating static pages (1/4) 
   Generating static pages (2/4) 
   Generating static pages (3/4) 
 ✓ Generating static pages (4/4)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                         Size  First Load JS
┌ ○ /                                            9.46 kB         159 kB
├ ○ /_not-found                                    977 B         102 kB
├ ƒ /api/auth/login                                166 B         101 kB
├ ƒ /api/auth/logout                               166 B         101 kB
├ ƒ /api/auth/register                             166 B         101 kB
├ ƒ /api/documents/[id]                            166 B         101 kB
├ ƒ /api/documents/extract-text                    166 B         101 kB
├ ƒ /api/documents/search                          166 B         101 kB
├ ƒ /api/documents/upload                          166 B         101 kB
├ ƒ /api/projects                                  166 B         101 kB
├ ƒ /api/projects/[id]                             166 B         101 kB
├ ƒ /api/projects/[id]/documents                   166 B         101 kB
├ ƒ /api/projects/[id]/qa-reviews                  166 B         101 kB
├ ƒ /api/projects/[id]/steps/[stepId]/complete     166 B         101 kB
├ ƒ /api/projects/[id]/steps/[stepId]/questions    166 B         101 kB
├ ƒ /dashboard                                   5.93 kB         155 kB
├ ƒ /login                                       3.73 kB         177 kB
├ ƒ /projects                                     3.7 kB         153 kB
├ ƒ /projects/[id]                               6.57 kB         156 kB
├ ƒ /projects/[id]/documents                     5.44 kB         205 kB
├ ƒ /projects/[id]/wizard/[stepId]               4.38 kB         197 kB
├ ƒ /projects/new                                4.05 kB         174 kB
├ ƒ /qa-engine                                   16.6 kB         207 kB
└ ƒ /register                                    3.84 kB         177 kB
+ First Load JS shared by all                     101 kB
  ├ chunks/4bd1b696-88c914d9989ad21f.js          53.2 kB
  ├ chunks/684-c3718e6e4ff08ce4.js               45.9 kB
  └ other shared chunks (total)                  1.92 kB


○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

